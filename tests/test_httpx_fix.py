#!/usr/bin/env python3
"""
测试httpx修复的脚本
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

def test_httpx_download():
    """测试httpx下载功能"""
    print("开始测试httpx下载功能...")
    
    try:
        from src.domain.services.knowledge_service import KnowledgeService
        
        # 创建服务实例
        service = KnowledgeService()
        
        # 测试下载功能
        test_url = "https://httpbin.org/bytes/1024"  # 使用httpbin作为测试URL
        
        print(f"测试下载URL: {test_url}")
        
        # 调用下载方法
        content = service._download_file_from_url_sync(test_url)
        
        if content:
            print(f"✅ 下载成功，内容大小: {len(content)} bytes")
            return True
        else:
            print("❌ 下载失败")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_httpx_upload():
    """测试httpx上传功能"""
    print("开始测试httpx上传功能...")
    
    try:
        from src.domain.services.knowledge_service import KnowledgeService
        
        # 创建服务实例
        service = KnowledgeService()
        
        # 测试上传功能
        test_url = "https://httpbin.org/put"  # 使用httpbin作为测试URL
        test_content = b"test content"
        test_filename = "test.txt"
        
        print(f"测试上传URL: {test_url}")
        
        # 调用上传方法
        success = service._upload_single_file_to_cloud_storage_sync(
            upload_url=test_url,
            file_content=test_content,
            file_name=test_filename
        )
        
        if success:
            print("✅ 上传成功")
            return True
        else:
            print("❌ 上传失败")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("=== 测试 httpx 修复 ===")
    
    # 测试下载
    download_success = test_httpx_download()
    
    # 测试上传
    upload_success = test_httpx_upload()
    
    if download_success and upload_success:
        print("✅ 所有测试通过！")
        sys.exit(0)
    else:
        print("❌ 部分测试失败")
        sys.exit(1)
