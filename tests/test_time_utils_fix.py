#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修复后的时间工具方法
验证时区参数功能是否正确工作
"""

import sys
import os

# 添加项目根目录到Python路径
project_root = os.path.abspath(os.path.dirname(__file__))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from datetime import datetime, timezone
from src.domain.utils.time_utils import TimeUtils, EAST_ASIA_TZ, UTC_TZ, EST_TZ


def test_to_iso8601_utc_with_timezone():
    """测试带时区参数的 to_iso8601_utc 方法"""
    print("=== 测试 to_iso8601_utc 方法（带时区参数）===")
    
    # 测试用例1: 东八区时间 2025-08-05 17:41:00
    east_asia_time = datetime(2025, 8, 5, 17, 41, 0)  # 无时区信息
    result = TimeUtils.to_iso8601_utc(east_asia_time, EAST_ASIA_TZ)
    expected = "2025-08-05T09:41Z"  # UTC 时间应该是 17:41 - 8 = 09:41
    print(f"东八区时间: {east_asia_time}")
    print(f"转换结果: {result}")
    print(f"期望结果: {expected}")
    print(f"测试通过: {result == expected}")
    print()
    
    # 测试用例2: 美国东部时间 2025-08-05 12:00:00
    est_time = datetime(2025, 8, 5, 12, 0, 0)  # 无时区信息
    result2 = TimeUtils.to_iso8601_utc(est_time, EST_TZ)
    expected2 = "2025-08-05T17:00Z"  # UTC 时间应该是 12:00 + 5 = 17:00
    print(f"美国东部时间: {est_time}")
    print(f"转换结果: {result2}")
    print(f"期望结果: {expected2}")
    print(f"测试通过: {result2 == expected2}")
    print()
    
    # 测试用例3: 带时区信息的时间
    east_asia_time_with_tz = datetime(2025, 8, 5, 17, 41, 0, tzinfo=EAST_ASIA_TZ)
    result3 = TimeUtils.to_iso8601_utc(east_asia_time_with_tz)
    print(f"带时区的东八区时间: {east_asia_time_with_tz}")
    print(f"转换结果: {result3}")
    print(f"期望结果: {expected}")
    print(f"测试通过: {result3 == expected}")
    print()
    
    # 测试用例4: 不指定时区（默认 UTC）
    utc_time = datetime(2025, 8, 5, 9, 41, 0)  # 无时区信息
    result4 = TimeUtils.to_iso8601_utc(utc_time)  # 不指定时区，默认 UTC
    expected4 = "2025-08-05T09:41Z"
    print(f"UTC时间（无时区信息）: {utc_time}")
    print(f"转换结果: {result4}")
    print(f"期望结果: {expected4}")
    print(f"测试通过: {result4 == expected4}")
    print()


def test_format_datetime_with_timezone():
    """测试带时区参数的 format_datetime 方法"""
    print("=== 测试 format_datetime 方法（带时区参数）===")
    
    # 测试用例: 东八区时间 2025-08-05 17:41:00
    east_asia_time = datetime(2025, 8, 5, 17, 41, 0)
    result = TimeUtils.format_datetime(east_asia_time, source_timezone=EAST_ASIA_TZ)
    expected = "2025-08-05T09:41Z"
    print(f"东八区时间: {east_asia_time}")
    print(f"转换结果: {result}")
    print(f"期望结果: {expected}")
    print(f"测试通过: {result == expected}")
    print()


def test_now_iso8601_utc():
    """测试 now_iso8601_utc 方法"""
    print("=== 测试 now_iso8601_utc 方法 ===")
    
    result = TimeUtils.now_iso8601_utc()
    print(f"当前 UTC 时间 (ISO 8601): {result}")
    print()


def test_from_iso8601_utc():
    """测试 from_iso8601_utc 方法"""
    print("=== 测试 from_iso8601_utc 方法 ===")
    
    # 测试用例: 解析 "2025-08-05T09:41Z"
    iso_str = "2025-08-05T09:41Z"
    result = TimeUtils.from_iso8601_utc(iso_str)
    print(f"ISO 字符串: {iso_str}")
    print(f"解析结果: {result}")
    print(f"时区信息: {result.tzinfo}")
    print()


def test_timezone_constants():
    """测试时区常量"""
    print("=== 测试时区常量 ===")
    
    print(f"东八区: {EAST_ASIA_TZ}")
    print(f"UTC时区: {UTC_TZ}")
    print(f"美国东部时间: {EST_TZ}")
    print()


if __name__ == "__main__":
    test_timezone_constants()
    test_to_iso8601_utc_with_timezone()
    test_format_datetime_with_timezone()
    test_now_iso8601_utc()
    test_from_iso8601_utc()
    
    print("=== 所有测试完成 ===") 