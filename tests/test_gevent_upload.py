#!/usr/bin/env python3
"""
测试gevent上传功能的脚本
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from src.domain.services.knowledge_service import KnowledgeService
from src.shared.auth.akless_auth import AuthContext
from src.shared.config.dev_config import DevConfig
from src.infrastructure.database.connection import DatabaseConnection

def test_gevent_upload():
    """测试gevent上传功能"""
    print("开始测试gevent上传功能...")
    
    # 初始化配置
    config = DevConfig()
    
    # 初始化数据库连接
    db_connection = DatabaseConnection(config)
    
    # 创建知识库服务实例
    knowledge_service = KnowledgeService(config, db_connection)
    
    # 模拟认证上下文
    auth_context = AuthContext(
        ali_uid="test_ali_uid",
        wy_id="test_wy_id",
        account_type="test_account"
    )
    
    # 模拟文档数据
    from src.domain.models.kb_document import KbDocumentModel
    new_documents = [
        KbDocumentModel(
            doc_id="test_doc_1",
            oss_bucket="test_bucket",
            oss_object_name="test_object_1",
            file_title="test_file_1.txt",
            file_size=1024,
            status="ACTIVE",
            file_id="test_file_1"
        )
    ]
    
    document_download_urls = {
        "test_file_1": "https://httpbin.org/bytes/1024"  # 使用httpbin作为测试URL
    }
    
    print("调用gevent上传方法...")
    
    # 调用gevent上传方法
    try:
        result = knowledge_service._upload_file_to_cloud_storage_gevent(
            auth_context=auth_context,
            new_documents=new_documents,
            document_download_urls=document_download_urls
        )
        print(f"上传结果: {result}")
    except Exception as e:
        print(f"上传过程中出现异常: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_gevent_upload()
