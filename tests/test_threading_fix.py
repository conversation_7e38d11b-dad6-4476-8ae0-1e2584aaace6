#!/usr/bin/env python3
"""
测试线程池修复的脚本
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

def test_threading_upload():
    """测试线程池上传功能"""
    print("开始测试线程池上传功能...")
    
    try:
        from src.domain.services.knowledge_service import KnowledgeService
        from src.shared.auth.akless_auth import AuthContext
        
        # 创建服务实例
        service = KnowledgeService()
        
        # 模拟认证上下文
        auth_context = AuthContext(
            ali_uid="test_ali_uid",
            wy_id="test_wy_id",
            account_type="test_account"
        )
        
        # 模拟文档数据
        from src.domain.models.kb_document import KbDocumentModel
        new_documents = [
            KbDocumentModel(
                doc_id="test_doc_1",
                oss_bucket="test_bucket",
                oss_object_name="test_object_1",
                file_title="test_file_1.txt",
                file_size=1024,
                status="ACTIVE",
                file_id="test_file_1"
            )
        ]
        
        document_download_urls = {
            "test_file_1": "https://httpbin.org/bytes/1024"  # 使用httpbin作为测试URL
        }
        
        print("调用线程池上传方法...")
        
        # 调用线程池上传方法
        try:
            service._upload_file_to_cloud_storage_sync(
                auth_context=auth_context,
                new_documents=new_documents,
                document_download_urls=document_download_urls
            )
            print("✅ 线程池上传测试完成")
            return True
        except Exception as e:
            print(f"上传过程中出现异常: {e}")
            import traceback
            traceback.print_exc()
            return False
            
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_threading_upload()
    if success:
        print("✅ 所有测试通过！")
        sys.exit(0)
    else:
        print("❌ 测试失败")
        sys.exit(1)
