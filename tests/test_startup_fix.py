#!/usr/bin/env python3
"""
测试启动修复的脚本
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

def test_import():
    """测试模块导入是否正常"""
    print("开始测试模块导入...")
    
    try:
        # 测试导入knowledge_service模块
        from src.domain.services.knowledge_service import KnowledgeService
        print("✅ KnowledgeService 导入成功")
        
        # 测试创建实例
        service = KnowledgeService()
        print("✅ KnowledgeService 实例创建成功")
        
        print("✅ 所有测试通过，启动修复成功！")
        return True
        
    except Exception as e:
        print(f"❌ 导入失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_import()
    sys.exit(0 if success else 1)
