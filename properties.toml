# Alpha Service - Dynaconf Configuration
# 基础配置 (所有环境的默认值)
[default]
# API配置
api_host = "0.0.0.0"
api_port = 8000
api_debug = false

# 数据库配置
db_mysql_host = "localhost"
db_mysql_port = 3306
db_mysql_user = "root"
db_mysql_password = ""
db_mysql_name = "alpha_service"

# KeyCenter配置
kc_server = ""
kc_app_code = ""
kc_key_name = ""

# Diamond配置
diamond_endpoint = ""
diamond_data_id = ""

# 日志配置
log_level = "INFO"
log_file_path = "logs/alpha-service.log"

# Memory MQ配置
mq_enabled = true
mq_endpoint = ""
mq_access_key = ""
mq_secret_key = ""
mq_instance_id = ""
mq_topic = "waiy_memory_event"
mq_group_id = "GID_WAIY_MESSAGE"

# Redis配置
redis_enabled = true
redis_host = "localhost"
redis_port = 6379
redis_username = ""
redis_password = ""
redis_db = 0
redis_max_connections = 50  # 增加Redis连接池大小
redis_socket_timeout = 10   # 增加超时时间
redis_socket_connect_timeout = 10

# Memory SDK数据库配置
memory_db_host = "localhost"
memory_db_port = 3306
memory_db_user = "waiy_infra"
memory_db_password = "waiy_infra2025"
memory_db_name = "waiy_infra"

# OSS配置
oss_enabled = true
oss_region = "cn-hangzhou"
oss_bucket_name = "alpha-service-oss"
oss_endpoint = ""
oss_access_key = ""
oss_secret_key = ""

# RAG OSS配置 (用于访问RAG服务的OSS bucket)
rag_oss_enabled = true
rag_oss_region = "cn-hangzhou"
rag_oss_bucket_name = ""
rag_oss_endpoint = "https://oss-cn-hangzhou.aliyuncs.com"
rag_oss_access_key = ""
rag_oss_secret_key = ""

# 阿里云无密钥认证配置
ram_role_arn = ""
region_id = "cn-hangzhou"
app_group = ""
akless_env = ""

# AgentBay配置
agentbay_cloud_resource_id = ""
agentbay_aliuid = ""

# Daily 环境配置 (开发环境)
[daily]
api_debug = true
db_mysql_host = "rm-bp1r55ujiutq3m3dxno.mysql.rds.aliyuncs.com"
db_mysql_user = "wuyingagent"
db_mysql_password = "Alpha123"
kc_server = "https://daily-keycenter.alibaba.net/keycenter"
kc_app_code = "567396056591492391061feae066cad7"
kc_key_name = "wuying-alpha-service_aone_key"
diamond_endpoint = "http://jmenv.tbsite.net:8080/diamond-server/diamond"
diamond_data_id = "alpha-service:application-daily"
log_level = "DEBUG"
log_file_path = "/home/<USER>/logs/application.log"
mq_user_name = "Ak3Z3S929Y1H1JFD"
mq_password = "j4H9UnA4au5LLb1A"
mq_endpoint = "rmq-cn-8t84bmpnk03.cn-hangzhou.rmq.aliyuncs.com:8080"
mq_access_key = "enc:p7+MGE9xxXwkLAyyGYgKLfenTwC9VGGQWp0B51GMHMY="
mq_secret_key = "enc:fstgS+DCyHyl2ruLtjdFplrTnW+lNnFS+ezBkkM48d4="
mq_instance_id = "rmq-cn-8t84bmpnk03"
mq_topic = "waiy_memory_event"
mq_group_id = "GID_WAIY_MESSAGE"
memory_db_host = "rm-bp1ki02906bm0c817zo.mysql.rds.aliyuncs.com"
memory_db_port = 3306
memory_db_user = "waiy_infra"
memory_db_password = "waiy_infra2025"
memory_db_name = "waiy_infra"
oss_bucket_name = "alpha-service-oss"
oss_endpoint = "https://alpha.wuying.com"
rag_oss_endpoint = "http://pre-waiy-memory-bucket.wuying.com"
rag_oss_bucket_name = "waiy-long-memory-bucket-pre"
aippt_endpoint = "co.aippt.cn"
aippt_access_key = "685b9513d90b3"
aippt_secret_key = "TzKB1Y4q7ADxoq4S9qadGlderDB2khgy"
aippt_channel = "alpha"
ram_role_arn = "acs:ram::1550203943326350:role/wuying-alpha-daily"
region_id = "cn-hangzhou"
app_group = ""
akless_env = "testing"
redis_host = "r-bp17olj91iehmsnzappd.redis.rds.aliyuncs.com"
redis_port = 6379
redis_password = "wuying:WuyingAI123"
redis_db = 0
agentbay_cloud_resource_id = "akm-2f6c64e0-ba79-47cb-9847-d265773c7873"
agentbay_aliuid = "1550203943326350"


# Pre 环境配置 (预发环境)
[pre]
api_debug = true
db_mysql_host = "rm-bp1r55ujiutq3m3dx.mysql.rds.aliyuncs.com"
db_mysql_user = "wuyingagent"
db_mysql_password = "Alpha123"
kc_server = "http://ep-bp1i236a392ebbb4666a.epsrv-bp1l8jcdtjed71636a8j.cn-hangzhou.privatelink.aliyuncs.com/keycenter"
kc_app_code = "ff51122b49334b8f9c7363b3968a2304"
kc_key_name = "wuying-alpha-service_aone_key"
diamond_endpoint = "http://pre-diamond.internal/diamond-server/diamond"
diamond_data_id = "alpha-service:application"
log_level = "DEBUG"
log_file_path = "/home/<USER>/wuying-alpha-service/logs/application.log"
mq_user_name = "Ak3Z3S929Y1H1JFD"
mq_password = "j4H9UnA4au5LLb1A"
mq_endpoint = "rmq-cn-8t84bmpnk03.cn-hangzhou.rmq.aliyuncs.com:8080"
mq_access_key = "enc:nztiMQfF9m+ssrF8XLEdF6OJHHGgVJvcpwNMmW49poQ="
mq_secret_key = "enc:SvYqPqxdFFgjGrOCSGjLfO2awNyM5tqtUvtZ86EsdNU="
mq_instance_id = "rmq-cn-8t84bmpnk03"
mq_topic = "waiy_memory_event"
mq_group_id = "GID_WAIY_MESSAGE"
memory_db_host = "rm-bp1ki02906bm0c817.mysql.rds.aliyuncs.com"
memory_db_port = 3306
memory_db_user = "waiy_infra"
memory_db_password = "waiy_infra2025"
memory_db_name = "waiy_infra"
oss_bucket_name = "alpha-service-oss"
oss_endpoint = "https://alpha.wuying.com"
rag_oss_endpoint = "http://pre-waiy-memory-bucket.wuying.com"
rag_oss_bucket_name = "waiy-long-memory-bucket-pre"
aippt_endpoint = "co.aippt.cn"
aippt_access_key = "685b9513d90b3"
aippt_secret_key = "TzKB1Y4q7ADxoq4S9qadGlderDB2khgy"
aippt_channel = "alpha"
ram_role_arn = "acs:ram::1550203943326350:role/wuying-alpha"
region_id = "cn-hangzhou"
app_group = ""
akless_env = "production"
redis_host = "r-bp17olj91iehmsnzap.redis.rds.aliyuncs.com"
redis_port = 6379
redis_password = "wuying:WuyingAI123"
redis_db = 1
agentbay_cloud_resource_id = "akm-2f6c64e0-ba79-47cb-9847-d265773c7873"
agentbay_aliuid = "1550203943326350"

# Prod 环境配置 (生产环境)
[prod]
api_debug = false
db_mysql_host = "prod-mysql.internal"
db_mysql_user = "alpha_service"
db_mysql_password = "enc:your_encrypted_prod_password"
kc_server = "http://keycenter-service.alibaba-inc.com/keycenter"
kc_app_code = "alpha_service"
kc_key_name = "alpha_key"
diamond_endpoint = "http://diamond.internal/diamond-server/diamond"
diamond_data_id = "alpha-service:application"
log_level = "WARNING"
log_file_path = "logs/alpha-service.log"
mq_user_name = "Ak3Z3S929Y1H1JFD"
mq_password = "j4H9UnA4au5LLb1A"
mq_endpoint = "http://1550203943326350.mqrest.cn-hangzhou.aliyuncs.com"
mq_access_key = ""
mq_secret_key = ""
mq_instance_id = "MQ_INST_1550203943326350_BZfdZpBC"
memory_db_host = "prod-memory-mysql.internal"
memory_db_port = 3306
memory_db_user = "waiy_infra"
memory_db_password = "enc:your_encrypted_memory_db_password"
memory_db_name = "waiy_infra"
oss_bucket_name = "alpha-service-oss-prod"
oss_endpoint = "https://alpha.wuying.com"
rag_oss_bucket_name = "waiy-long-memory-bucket-prod"
aippt_endpoint = "co.aippt.cn"
aippt_access_key = "685b9513d90b3"
aippt_secret_key = "TzKB1Y4q7ADxoq4S9qadGlderDB2khgy"
aippt_channel = "alpha"
ram_role_arn = "acs:ram::1550203943326350:role/AliyunECSInstanceForOSSRole"
region_id = "cn-hangzhou"
app_group = "waiy-rag-service"
akless_env = "prod"
redis_host = "prod-redis.internal"
redis_port = 6379
redis_password = "enc:your_encrypted_redis_password"
redis_db = 0
agentbay_cloud_resource_id = "akm-prod-2f6c64e0-ba79-47cb-9847-d265773c7873"
agentbay_aliuid = "1550203943326350"