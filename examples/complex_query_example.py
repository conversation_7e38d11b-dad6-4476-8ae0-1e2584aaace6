#!/usr/bin/env python3
"""
复杂条件查询示例
演示如何使用 list_kb_documents_with_complex_conditions 方法
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from src.infrastructure.database.repositories.kb_documents_repository import kb_documents_repository
from src.infrastructure.database.models.knowledgebase_models import KbDocumentModel
from sqlalchemy import desc


def example_complex_query():
    """示例：复杂条件查询"""
    
    # 示例1：查询 session_id = "123" 且 ((file_id = "a" AND file_title = "b") OR (file_id = "c" AND file_title = "d"))
    print("=== 示例1：复杂条件查询 ===")
    
    file_conditions = [
        {"file_id": "a", "file_title": "b"},
        {"file_id": "c", "file_title": "d"}
    ]
    
    try:
        results = kb_documents_repository.list_kb_documents_with_complex_conditions(
            session_id="123",
            file_conditions=file_conditions
        )
        
        print(f"查询结果数量: {len(results)}")
        for doc in results:
            print(f"  - doc_id: {doc.doc_id}, file_id: {doc.file_id}, file_title: {doc.file_title}")
            
    except Exception as e:
        print(f"查询失败: {e}")
    
    print("\n" + "="*50 + "\n")
    
    # 示例2：只查询特定文件ID和文件名的组合
    print("=== 示例2：只查询文件条件 ===")
    
    file_conditions_only = [
        {"file_id": "file001", "file_title": "文档1"},
        {"file_id": "file002", "file_title": "文档2"}
    ]
    
    try:
        results = kb_documents_repository.list_kb_documents_with_complex_conditions(
            file_conditions=file_conditions_only,
            limit=10
        )
        
        print(f"查询结果数量: {len(results)}")
        for doc in results:
            print(f"  - doc_id: {doc.doc_id}, file_id: {doc.file_id}, file_title: {doc.file_title}")
            
    except Exception as e:
        print(f"查询失败: {e}")
    
    print("\n" + "="*50 + "\n")
    
    # 示例3：查询特定会话下的成功状态文档，并按创建时间倒序排列
    print("=== 示例3：带状态和排序的查询 ===")
    
    file_conditions_with_status = [
        {"file_id": "file001", "file_title": "文档1"},
        {"file_id": "file002"}  # 只指定file_id，不指定file_title
    ]
    
    try:
        results = kb_documents_repository.list_kb_documents_with_complex_conditions(
            session_id="session123",
            file_conditions=file_conditions_with_status,
            status="success",
            order_pairs=[desc(KbDocumentModel.gmt_created)],
            limit=5
        )
        
        print(f"查询结果数量: {len(results)}")
        for doc in results:
            print(f"  - doc_id: {doc.doc_id}, file_id: {doc.file_id}, file_title: {doc.file_title}, status: {doc.status}")
            
    except Exception as e:
        print(f"查询失败: {e}")


def example_sql_equivalent():
    """展示SQL等价语句"""
    print("=== SQL等价语句示例 ===")
    
    print("""
# 示例1的SQL等价语句：
SELECT * FROM kb_documents 
WHERE session_id = '123' 
  AND ((file_id = 'a' AND file_title = 'b') OR (file_id = 'c' AND file_title = 'd'))

# 示例2的SQL等价语句：
SELECT * FROM kb_documents 
WHERE (file_id = 'file001' AND file_title = '文档1') 
   OR (file_id = 'file002' AND file_title = '文档2')
LIMIT 10

# 示例3的SQL等价语句：
SELECT * FROM kb_documents 
WHERE session_id = 'session123' 
  AND status = 'success'
  AND ((file_id = 'file001' AND file_title = '文档1') OR file_id = 'file002')
ORDER BY gmt_created DESC
LIMIT 5
    """)


if __name__ == "__main__":
    print("复杂条件查询示例")
    print("="*50)
    
    example_complex_query()
    example_sql_equivalent()
