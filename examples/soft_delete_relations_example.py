#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
soft_delete_relations 批量删除功能使用示例
"""

import sys
import os

# 添加项目根目录到 Python 路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from infrastructure.database.repositories.kb_document_relations_repository import kb_document_relations_repository


def example_delete_specific_files():
    """示例：删除指定文件列表的关联"""
    print("=== 示例：删除指定文件列表的关联 ===")
    
    try:
        # 1. 创建测试数据
        kb_id = "example_kb_001"
        session_id = "example_session_001"
        file_ids = ["file_001", "file_002", "file_003", "file_004", "file_005"]
        
        print("1. 创建测试关联...")
        relations = kb_document_relations_repository.batch_create_relations(
            kb_id=kb_id,
            file_ids=file_ids,
            session_id=session_id
        )
        print(f"   创建了 {len(relations)} 个关联")
        
        # 2. 删除指定文件
        delete_file_ids = ["file_001", "file_003", "file_005"]
        print(f"2. 删除指定文件: {delete_file_ids}")
        
        deleted_count = kb_document_relations_repository.soft_delete_relations(
            kb_id=kb_id,
            file_ids=delete_file_ids
        )
        print(f"   删除了 {deleted_count} 个关联")
        
        # 3. 验证结果
        print("3. 验证删除结果...")
        remaining_relations = kb_document_relations_repository.list_relations(
            kb_id=kb_id,
            session_id=session_id
        )
        remaining_file_ids = [rel.file_id for rel in remaining_relations]
        print(f"   剩余文件: {remaining_file_ids}")
        
    except Exception as e:
        print(f"示例执行失败: {e}")


def example_delete_session_files():
    """示例：删除指定会话的所有文件关联"""
    print("\n=== 示例：删除指定会话的所有文件关联 ===")
    
    try:
        # 1. 创建测试数据
        kb_id = "example_kb_002"
        session_ids = ["session_001", "session_002", "session_003"]
        file_ids = ["file_001", "file_002", "file_003"]
        
        print("1. 创建测试关联...")
        for session_id in session_ids:
            relations = kb_document_relations_repository.batch_create_relations(
                kb_id=kb_id,
                file_ids=file_ids,
                session_id=session_id
            )
            print(f"   为会话 {session_id} 创建了 {len(relations)} 个关联")
        
        # 2. 删除指定会话的所有关联
        target_session_id = "session_002"
        print(f"2. 删除会话 {target_session_id} 的所有关联...")
        
        deleted_count = kb_document_relations_repository.soft_delete_relations(
            kb_id=kb_id,
            session_id=target_session_id
        )
        print(f"   删除了 {deleted_count} 个关联")
        
        # 3. 验证结果
        print("3. 验证删除结果...")
        for session_id in session_ids:
            remaining_relations = kb_document_relations_repository.list_relations(
                kb_id=kb_id,
                session_id=session_id
            )
            print(f"   会话 {session_id} 剩余关联: {len(remaining_relations)}")
        
    except Exception as e:
        print(f"示例执行失败: {e}")


def example_delete_all_kb_files():
    """示例：删除知识库的所有文件关联"""
    print("\n=== 示例：删除知识库的所有文件关联 ===")
    
    try:
        # 1. 创建测试数据
        kb_id = "example_kb_003"
        sessions = [
            ("session_001", ["file_001", "file_002"]),
            ("session_002", ["file_003", "file_004"]),
            ("session_003", ["file_005", "file_006"]),
        ]
        
        print("1. 创建测试关联...")
        total_relations = 0
        for session_id, file_ids in sessions:
            relations = kb_document_relations_repository.batch_create_relations(
                kb_id=kb_id,
                file_ids=file_ids,
                session_id=session_id
            )
            total_relations += len(relations)
            print(f"   为会话 {session_id} 创建了 {len(relations)} 个关联")
        
        print(f"   总共创建了 {total_relations} 个关联")
        
        # 2. 删除知识库的所有关联
        print("2. 删除知识库的所有关联...")
        
        deleted_count = kb_document_relations_repository.soft_delete_relations(
            kb_id=kb_id
        )
        print(f"   删除了 {deleted_count} 个关联")
        
        # 3. 验证结果
        print("3. 验证删除结果...")
        remaining_relations = kb_document_relations_repository.list_relations(
            kb_id=kb_id
        )
        print(f"   知识库剩余关联: {len(remaining_relations)}")
        
    except Exception as e:
        print(f"示例执行失败: {e}")


def example_delete_session_specific_files():
    """示例：删除指定会话中的指定文件"""
    print("\n=== 示例：删除指定会话中的指定文件 ===")
    
    try:
        # 1. 创建测试数据
        kb_id = "example_kb_004"
        session_id = "example_session_004"
        file_ids = ["file_001", "file_002", "file_003", "file_004", "file_005"]
        
        print("1. 创建测试关联...")
        relations = kb_document_relations_repository.batch_create_relations(
            kb_id=kb_id,
            file_ids=file_ids,
            session_id=session_id
        )
        print(f"   创建了 {len(relations)} 个关联")
        
        # 2. 删除指定会话中的指定文件
        delete_file_ids = ["file_002", "file_004"]
        print(f"2. 删除会话 {session_id} 中的指定文件: {delete_file_ids}")
        
        deleted_count = kb_document_relations_repository.soft_delete_relations(
            kb_id=kb_id,
            file_ids=delete_file_ids,
            session_id=session_id
        )
        print(f"   删除了 {deleted_count} 个关联")
        
        # 3. 验证结果
        print("3. 验证删除结果...")
        remaining_relations = kb_document_relations_repository.list_relations(
            kb_id=kb_id,
            session_id=session_id
        )
        remaining_file_ids = [rel.file_id for rel in remaining_relations]
        print(f"   会话剩余文件: {remaining_file_ids}")
        
    except Exception as e:
        print(f"示例执行失败: {e}")


if __name__ == "__main__":
    print("soft_delete_relations 批量删除功能使用示例")
    print("=" * 60)
    
    example_delete_specific_files()
    example_delete_session_files()
    example_delete_all_kb_files()
    example_delete_session_specific_files()

    print("\n所有示例执行完成！") 