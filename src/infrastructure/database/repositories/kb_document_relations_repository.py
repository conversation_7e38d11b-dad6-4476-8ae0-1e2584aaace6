"""
知识库文档关联数据库服务层
提供知识库文档关联的CRUD操作
"""

from datetime import datetime
from typing import Optional, List, Dict, Any

from loguru import logger
from sqlalchemy import and_, UnaryExpression

from ..connection import db_manager
from ..models.knowledgebase_models import KbDocumentRelationModel


class KbDocumentRelationRepository:
    """知识库文档关联数据库仓库"""

    def __init__(self):
        self.db_manager = db_manager

    def batch_create_relations(
        self, kb_id: str, file_map: Dict[str, str], session_id: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """批量创建知识库文档关联，返回 dict"""
        try:
            with self.db_manager.get_session() as db_session:
                relations = []
                for file_id, file_title in file_map.items():
                    relation = KbDocumentRelationModel(
                        kb_id=kb_id,
                        file_id=file_id,
                        file_title=file_title,
                        session_id=session_id,
                        is_deleted=0,
                    )
                    relations.append(relation)
                db_session.add_all(relations)
                db_session.flush()
                logger.info(
                    f"[KbDocumentRelationDB] 批量创建关联: kb_id={kb_id}, 文件数量={len(relations)}"
                )
                return [relation.to_dict() for relation in relations]
        except Exception as e:
            logger.error(f"[KbDocumentRelationDB] 批量创建关联失败: {e}")
            raise

    def get_relation(
        self, kb_id: str, file_id: str, file_title: Optional[str] = None, session_id_is_null: Optional[bool] = False
    ) -> Optional[KbDocumentRelationModel]:
        """根据kb_id和file_id获取关联"""
        try:
            with self.db_manager.get_session() as db_session:
                relation = (
                    db_session.query(KbDocumentRelationModel)
                    .filter(
                        and_(
                            KbDocumentRelationModel.kb_id == kb_id,
                            KbDocumentRelationModel.file_id == file_id,
                            KbDocumentRelationModel.file_title == file_title if file_title else True,
                            KbDocumentRelationModel.session_id.is_(None) if session_id_is_null else True,
                            KbDocumentRelationModel.is_deleted == 0,
                        )
                    )
                    .first()
                )

                if relation is not None:
                    db_session.refresh(relation)
                    db_session.expunge(relation)
                return relation
        except Exception as e:
            logger.error(f"[KbDocumentRelationDB] 获取关联失败: {e}")
            raise


    def update_relation_fileid_by_filetitle(
        self, kb_id: str, file_id: str, file_title: str
    ) -> bool:
        """根据文件名称更新关联的file_id"""
        try:
            with self.db_manager.get_session() as db_session:
                relation = db_session.query(KbDocumentRelationModel).filter(
                    and_(
                        KbDocumentRelationModel.kb_id == kb_id,
                        KbDocumentRelationModel.file_title == file_title,
                        KbDocumentRelationModel.is_deleted == 0,
                    )
                ).first()
                if relation:
                    relation.file_id = file_id
                    db_session.commit()
                    return True
                return False
        except Exception as e:
            logger.error(f"[KbDocumentRelationDB] 更新关联失败: {e}")
            raise
    def list_relations(
        self,
        kb_id: str,
        session_id: Optional[str] = None,
        file_title_list: Optional[List[str]] = None,
        file_id_list: Optional[List[str]] = None,
        session_id_is_null: Optional[
            bool
        ] = False,  # 是否覆盖session_id为空的关联，默认覆盖
        limit: Optional[int] = None,
        less_than_equal_id: Optional[int] = None,
        less_than_equal_gmt_create: Optional[datetime] = None,
        order_pairs: Optional[list[UnaryExpression]] = None,
    ) -> List[KbDocumentRelationModel]:
        """获取某知识库下所有文档关联"""
        try:
            with self.db_manager.get_session() as db_session:
                query = db_session.query(KbDocumentRelationModel).filter(
                    and_(
                        KbDocumentRelationModel.kb_id == kb_id,
                        KbDocumentRelationModel.is_deleted == 0,
                    )
                )
                if file_title_list:
                    query = query.filter(
                        KbDocumentRelationModel.file_title.in_(file_title_list)
                    )
                if file_id_list:
                    query = query.filter(KbDocumentRelationModel.file_id.in_(file_id_list))
                if session_id:
                    query = query.filter(
                        KbDocumentRelationModel.session_id == session_id
                    )
                if session_id_is_null:
                    query = query.filter(KbDocumentRelationModel.session_id.is_(None))
                if less_than_equal_id:
                    query = query.filter(
                        KbDocumentRelationModel.id <= less_than_equal_id
                    )
                if less_than_equal_gmt_create:
                    query = query.filter(
                        KbDocumentRelationModel.gmt_created
                        <= less_than_equal_gmt_create
                    )
                if order_pairs:
                    query = query.order_by(*order_pairs)
                if limit:
                    query = query.limit(limit)
                results = query.all()

                # 确保所有延迟加载的属性都被加载
                for obj in results:
                    db_session.refresh(obj)

                # 从 session 中分离所有对象，避免 session 结束后对象游离
                detached_results = []
                for obj in results:
                    db_session.expunge(obj)
                    detached_results.append(obj)
                return detached_results

        except Exception as e:
            logger.error(f"[KbDocumentRelationDB] 获取知识库关联失败: {e}")
            raise

    def soft_delete_relation(self, kb_id: str, file_id: str) -> bool:
        """软删除知识库文档关联（单个文件）"""
        return self.soft_delete_relations(kb_id=kb_id, file_ids=[file_id]) > 0

    def soft_delete_relations(
        self,
        kb_id: str,
        file_ids: Optional[List[str]] = None,
        file_title_list: Optional[List[str]] = None,
        session_id: Optional[str] = None,
        session_ids: Optional[List[str]] = None,
    ) -> int:
        """软删除知识库文档关联（支持批量删除）"""
        try:
            with self.db_manager.get_session() as db_session:
                # 构建查询条件
                conditions = [
                    KbDocumentRelationModel.kb_id == kb_id,
                    KbDocumentRelationModel.is_deleted == 0,
                ]

                # 如果指定了 file_ids，添加文件ID条件
                if file_ids:
                    conditions.append(KbDocumentRelationModel.file_id.in_(file_ids))

                if file_title_list:
                    conditions.append(
                        KbDocumentRelationModel.file_title.in_(file_title_list)
                    )

                # 如果指定了 session_id，添加会话ID条件
                if session_id:
                    conditions.append(KbDocumentRelationModel.session_id == session_id)

                if session_ids:
                    conditions.append(
                        KbDocumentRelationModel.session_id.in_(session_ids)
                    )

                # 查询要删除的关联
                relations = (
                    db_session.query(KbDocumentRelationModel)
                    .filter(and_(*conditions))
                    .all()
                )

                if not relations:
                    logger.warning(
                        f"[KbDocumentRelationDB] 没有找到要删除的关联: kb_id={kb_id}, "
                        f"file_ids={file_ids}, session_id={session_id}"
                    )
                    return 0

                # 批量软删除
                current_time = datetime.now()
                for relation in relations:
                    relation.is_deleted = relation.id
                    relation.gmt_modified = current_time

                # 提交事务
                db_session.commit()

                logger.info(
                    f"[KbDocumentRelationDB] 批量软删除关联成功: kb_id={kb_id}, "
                    f"删除数量={len(relations)}, file_ids={file_ids}, session_id={session_id}"
                )
                return len(relations)

        except Exception as e:
            logger.error(f"[KbDocumentRelationDB] 批量软删除关联失败: {e}")
            raise

    def count_relations(
        self,
        kb_id: Optional[str] = None,
        file_id: Optional[str] = None,
        file_title_list: Optional[List[str]] = None,
        session_id: Optional[str] = None,
        session_id_is_null: Optional[bool] = False,
    ) -> int:
        """统计某知识库下的文档关联数量"""
        try:
            with self.db_manager.get_session() as db_session:
                query = db_session.query(KbDocumentRelationModel).filter(
                    and_(
                        KbDocumentRelationModel.is_deleted == 0,
                    )
                )
                if kb_id:
                    query = query.filter(
                        KbDocumentRelationModel.kb_id == kb_id
                    )
                if file_id:
                    query = query.filter(
                        KbDocumentRelationModel.file_id == file_id
                    )
                if session_id:
                    query = query.filter(
                        KbDocumentRelationModel.session_id == session_id
                    )
                if file_title_list:
                    query = query.filter(
                        KbDocumentRelationModel.file_title.in_(file_title_list)
                    )
                if session_id_is_null:
                    query = query.filter(KbDocumentRelationModel.session_id.is_(None))
                return query.count()
        except Exception as e:
            logger.error(f"[KbDocumentRelationDB] 统计关联数量失败: {e}")
            raise

# 全局实例
kb_document_relations_repository = KbDocumentRelationRepository()
