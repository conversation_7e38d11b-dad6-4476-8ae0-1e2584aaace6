"""
Session数据库服务层
提供Session的CRUD操作
"""

import uuid
import asyncio
from datetime import datetime
from typing import Optional, List, Dict, Any
from sqlalchemy.orm import Session as SQLSession
from sqlalchemy.exc import IntegrityError
from sqlalchemy import func
from loguru import logger

from ..models.session_models import SessionModel
from ..connection import db_manager


class SessionDatabaseService:
    """Session数据库服务"""
    
    def __init__(self):
        self.db_manager = db_manager
    
    def create_session(
        self,
        ali_uid: str,
        agent_id: str,
        session_id: Optional[str] = None,
        title: str = "",
        metadata: Optional[Dict[str, Any]] = None,
        wy_id: Optional[str] = None
    ) -> SessionModel:
        """创建新的Session"""
        try:
            # 生成session_id（如果未提供）
            if not session_id:
                session_id = f"sess_{uuid.uuid4().hex[:12]}"
            
            # 创建Session模型
            session_model = SessionModel(
                session_id=session_id,
                ali_uid=ali_uid,
                agent_id=agent_id,
                title=title,
                status='CREATE',
                meta_data=metadata or {},
                wy_id=wy_id
            )
            
            # 保存到数据库
            with self.db_manager.get_session() as db_session:
                db_session.add(session_model)
                db_session.flush()  # 获取自增ID
                
                logger.info(f"[SessionDB] 创建Session: {session_id} (ID: {session_model.id})")
                
                # 🔧 创建一个新的SessionModel实例，避免SQLAlchemy Session绑定问题
                detached_model = SessionModel(
                    id=session_model.id,
                    session_id=session_model.session_id,
                    ali_uid=session_model.ali_uid,
                    agent_id=session_model.agent_id,
                    title=session_model.title,
                    status=session_model.status,
                    gmt_create=session_model.gmt_create,
                    gmt_modified=session_model.gmt_modified,
                    meta_data=session_model.meta_data,
                    wy_id=session_model.wy_id
                )
                return detached_model
                
        except IntegrityError as e:
            logger.error(f"[SessionDB] Session已存在: {session_id}")
            raise ValueError(f"Session ID已存在: {session_id}")
        except Exception as e:
            logger.error(f"[SessionDB] 创建Session失败: {e}")
            raise
    
    def get_session_by_id(self, session_id: str) -> Optional[SessionModel]:
        """根据session_id获取Session"""
        try:
            with self.db_manager.get_session() as db_session:
                session_model = db_session.query(SessionModel).filter(
                    SessionModel.session_id == session_id
                ).first()

                if session_model:
                    logger.info(f"[SessionDB] 获取Session: {session_id}")

                    # 🔧 创建一个新的SessionModel实例，避免SQLAlchemy Session绑定问题
                    detached_model = SessionModel(
                        id=session_model.id,
                        session_id=session_model.session_id,
                        ali_uid=session_model.ali_uid,
                        agent_id=session_model.agent_id,
                        title=session_model.title,
                        status=session_model.status,
                        gmt_create=session_model.gmt_create,
                        gmt_modified=session_model.gmt_modified,
                        meta_data=session_model.meta_data,
                        wy_id=session_model.wy_id
                    )
                    return detached_model
                else:
                    logger.info(f"[SessionDB] Session不存在: {session_id}")
                    return None

        except Exception as e:
            logger.error(f"[SessionDB] 获取Session失败: {e}")
            raise

    async def get_session_by_id_async(self, session_id: str) -> Optional[SessionModel]:
        """异步根据session_id获取Session"""
        try:
            def _get_session():
                with self.db_manager.get_session() as db_session:
                    session_model = db_session.query(SessionModel).filter(
                        SessionModel.session_id == session_id
                    ).first()

                    if session_model:
                        logger.info(f"[SessionDB] 异步获取Session: {session_id}")

                        # 创建一个新的SessionModel实例，避免SQLAlchemy Session绑定问题
                        detached_model = SessionModel(
                            id=session_model.id,
                            session_id=session_model.session_id,
                            ali_uid=session_model.ali_uid,
                            agent_id=session_model.agent_id,
                            title=session_model.title,
                            status=session_model.status,
                            gmt_create=session_model.gmt_create,
                            gmt_modified=session_model.gmt_modified,
                            meta_data=session_model.meta_data,
                            wy_id=session_model.wy_id
                        )
                        return detached_model
                    else:
                        logger.info(f"[SessionDB] 异步Session不存在: {session_id}")
                        return None

            return await asyncio.to_thread(_get_session)

        except Exception as e:
            logger.error(f"[SessionDB] 异步获取Session失败: {e}")
            raise

    def get_sessions_by_ids(self, session_ids: List[str]) -> List[SessionModel]:
        """根据session_id列表批量获取Sessions"""
        try:
            if not session_ids:
                logger.debug("[SessionDB] 批量查询Session: 空列表")
                return []

            with self.db_manager.get_session() as db_session:
                session_models = db_session.query(SessionModel).filter(
                    SessionModel.session_id.in_(session_ids)
                ).all()

                logger.info(f"[SessionDB] 批量获取Sessions: 查询{len(session_ids)}个, 找到{len(session_models)}个")

                # 🔧 创建detached对象避免Session绑定问题
                detached_sessions = []
                for session in session_models:
                    detached_session = SessionModel(
                        id=session.id,
                        session_id=session.session_id,
                        ali_uid=session.ali_uid,
                        agent_id=session.agent_id,
                        title=session.title,
                        status=session.status,
                        gmt_create=session.gmt_create,
                        gmt_modified=session.gmt_modified,
                        meta_data=session.meta_data,
                        wy_id=session.wy_id
                    )
                    detached_sessions.append(detached_session)

                return detached_sessions

        except Exception as e:
            logger.error(f"[SessionDB] 批量获取Sessions失败: {e}")
            raise
    
    def update_session_status(
        self,
        session_id: str,
        status: str,
        title: Optional[str] = None
    ) -> bool:
        """更新Session状态"""
        try:
            with self.db_manager.get_session() as db_session:
                session_model = db_session.query(SessionModel).filter(
                    SessionModel.session_id == session_id
                ).first()
                
                if not session_model:
                    logger.warning(f"[SessionDB] Session不存在: {session_id}")
                    return False
                
                # 更新状态
                old_status = session_model.status
                session_model.status = status
                session_model.gmt_modified = datetime.now()
                
                # 更新标题（如果提供）
                if title is not None:
                    session_model.title = title
                
                logger.info(f"[SessionDB] 更新Session状态: {session_id} {old_status} -> {status}")
                return True
                
        except Exception as e:
            logger.error(f"[SessionDB] 更新Session状态失败: {e}")
            raise
    
    def update_session_metadata(
        self, 
        session_id: str, 
        metadata: Dict[str, Any]
    ) -> bool:
        """更新Session元数据"""
        try:
            with self.db_manager.get_session() as db_session:
                session_model = db_session.query(SessionModel).filter(
                    SessionModel.session_id == session_id
                ).first()
                
                if not session_model:
                    logger.warning(f"[SessionDB] Session不存在: {session_id}")
                    return False
                
                # 更新元数据
                session_model.meta_data = metadata
                session_model.gmt_modified = datetime.now()
                
                logger.debug(f"[SessionDB] 更新Session元数据: {session_id}")
                return True
                
        except Exception as e:
            logger.error(f"[SessionDB] 更新Session元数据失败: {e}")
            raise
    
    def list_sessions_by_user(
        self,
        ali_uid: str,
        limit: int = 20,
        offset: int = 0,
        status_filter: Optional[str] = None
    ) -> List[SessionModel]:
        """按用户列出Sessions"""
        try:
            with self.db_manager.get_session() as db_session:
                query = db_session.query(SessionModel).filter(
                    SessionModel.ali_uid == ali_uid
                )
                
                # 状态过滤
                if status_filter:
                    query = query.filter(SessionModel.status == status_filter)
                
                # 排序和分页 - 优先按修改时间，然后按创建时间倒序
                sessions = query.order_by(
                    SessionModel.gmt_modified.desc(),
                    SessionModel.gmt_create.desc()
                ).offset(offset).limit(limit).all()
                
                logger.debug(f"[SessionDB] 查询用户Sessions: {ali_uid}, 数量: {len(sessions)}")
                return sessions
                
        except Exception as e:
            logger.error(f"[SessionDB] 查询用户Sessions失败: {e}")
            raise
    
    def count_sessions_by_user(
        self,
        ali_uid: str,
        status_filter: Optional[str] = None
    ) -> int:
        """统计用户的Session数量"""
        try:
            with self.db_manager.get_session() as db_session:
                query = db_session.query(SessionModel).filter(
                    SessionModel.ali_uid == ali_uid
                )
                
                if status_filter:
                    query = query.filter(SessionModel.status == status_filter)
                
                count = query.count()
                logger.debug(f"[SessionDB] 用户Session数量: {ali_uid} = {count}")
                return count
                
        except Exception as e:
            logger.error(f"[SessionDB] 统计Session数量失败: {e}")
            raise
    
    def delete_session(self, session_id: str) -> bool:
        """删除Session"""
        try:
            with self.db_manager.get_session() as db_session:
                session_model = db_session.query(SessionModel).filter(
                    SessionModel.session_id == session_id
                ).first()
                
                if not session_model:
                    logger.warning(f"[SessionDB] Session不存在: {session_id}")
                    return False
                
                db_session.delete(session_model)
                logger.info(f"[SessionDB] 删除Session: {session_id}")
                return True
                
        except Exception as e:
            logger.error(f"[SessionDB] 删除Session失败: {e}")
            raise
    
    def cleanup_expired_sessions(self, hours: int = 24) -> int:
        """清理过期Sessions"""
        try:
            from datetime import timedelta
            
            cutoff_time = datetime.now() - timedelta(hours=hours)
            
            with self.db_manager.get_session() as db_session:
                # 查询过期的Sessions（只清理CLOSED状态的会话）
                expired_sessions = db_session.query(SessionModel).filter(
                    SessionModel.gmt_modified < cutoff_time,
                    SessionModel.status == 'CLOSED'
                ).all()
                
                count = len(expired_sessions)
                
                # 删除过期Sessions
                for session in expired_sessions:
                    db_session.delete(session)
                
                if count > 0:
                    logger.info(f"[SessionDB] 清理过期Sessions: {count}个")
                
                return count
                
        except Exception as e:
            logger.error(f"[SessionDB] 清理过期Sessions失败: {e}")
            raise
    
    def list_sessions(
        self,
        limit: int = 50,
        offset: int = 0,
        ali_uid: Optional[str] = None,
        agent_id: Optional[str] = None,
        status_filter: Optional[str] = None,
        next_token: Optional[str] = None,
        search_keyword: Optional[str] = None
    ) -> List[SessionModel]:
        """列出所有Sessions（支持过滤和next_token分页）"""
        try:
            from domain.utils.next_token_utils import NextTokenUtils
            
            with self.db_manager.get_session() as db_session:
                query = db_session.query(SessionModel)
                
                # 用户过滤
                if ali_uid:
                    query = query.filter(SessionModel.ali_uid == ali_uid)
                
                # Agent过滤
                if agent_id:
                    query = query.filter(SessionModel.agent_id == agent_id)
                
                # 状态过滤
                if status_filter:
                    if status_filter.startswith("!"):
                        # 排除特定状态，如 "!DELETE" 表示排除DELETE状态
                        exclude_status = status_filter[1:]
                        query = query.filter(SessionModel.status != exclude_status)
                    else:
                        # 包含特定状态
                        query = query.filter(SessionModel.status == status_filter)

                # 搜索关键词过滤（标题模糊查询）
                if search_keyword:
                    # 去除前后空格
                    search_keyword = search_keyword.strip()
                    if search_keyword:
                        # 使用LIKE进行模糊查询，支持部分匹配
                        query = query.filter(SessionModel.title.like(f"%{search_keyword}%"))
                        logger.debug(f"[SessionDB] 应用搜索过滤: keyword='{search_keyword}'")

                # next_token 分页逻辑
                if next_token:
                    pagination_model = NextTokenUtils.decode(next_token)
                    if pagination_model.gmt and pagination_model.id:
                        # 按修改时间倒序，如果时间相同则按ID倒序
                        query = query.filter(
                            (SessionModel.gmt_modified < pagination_model.gmt) |
                            ((SessionModel.gmt_modified == pagination_model.gmt) & (SessionModel.id < pagination_model.id))
                        )
                
                # 排序 - 优先按修改时间，然后按ID倒序（用于next_token分页）
                sessions = query.order_by(
                    SessionModel.gmt_modified.desc(),
                    SessionModel.id.desc()
                ).limit(limit).all()
                
                logger.debug(f"[SessionDB] 查询Sessions: 数量={len(sessions)}, limit={limit}, offset={offset}")
                
                # 🔧 创建detached对象避免Session绑定问题
                detached_sessions = []
                for session in sessions:
                    detached_session = SessionModel(
                        id=session.id,
                        session_id=session.session_id,
                        ali_uid=session.ali_uid,
                        agent_id=session.agent_id,
                        title=session.title,
                        status=session.status,
                        gmt_create=session.gmt_create,
                        gmt_modified=session.gmt_modified,
                        meta_data=session.meta_data,
                        wy_id=session.wy_id
                    )
                    detached_sessions.append(detached_session)
                
                return detached_sessions
                
        except Exception as e:
            logger.error(f"[SessionDB] 查询Sessions失败: {e}")
            raise

    async def list_sessions_async(
        self,
        limit: int = 50,
        offset: int = 0,
        ali_uid: Optional[str] = None,
        agent_id: Optional[str] = None,
        status_filter: Optional[str] = None,
        next_token: Optional[str] = None,
        search_keyword: Optional[str] = None
    ) -> List[SessionModel]:
        """异步列出所有Sessions（支持过滤和next_token分页）"""
        try:
            def _list_sessions():
                from domain.utils.next_token_utils import NextTokenUtils

                # 确保可以访问外部作用域的变量
                nonlocal limit, offset, ali_uid, agent_id, status_filter, next_token, search_keyword

                with self.db_manager.get_session() as db_session:
                    query = db_session.query(SessionModel)

                    # 用户过滤
                    if ali_uid:
                        query = query.filter(SessionModel.ali_uid == ali_uid)

                    # Agent过滤
                    if agent_id:
                        query = query.filter(SessionModel.agent_id == agent_id)

                    # 状态过滤
                    if status_filter:
                        if status_filter.startswith("!"):
                            # 排除指定状态
                            exclude_status = status_filter[1:]
                            query = query.filter(SessionModel.status != exclude_status)
                            logger.debug(f"[SessionDB] 异步排除状态过滤: exclude_status='{exclude_status}'")
                        else:
                            # 包含指定状态
                            query = query.filter(SessionModel.status == status_filter)
                            logger.debug(f"[SessionDB] 异步状态过滤: status='{status_filter}'")

                    # 搜索关键词过滤（标题模糊查询）
                    if search_keyword:
                        # 去除前后空格
                        search_keyword = search_keyword.strip()
                        if search_keyword:
                            # 使用LIKE进行模糊查询，支持部分匹配
                            query = query.filter(SessionModel.title.like(f"%{search_keyword}%"))
                            logger.debug(f"[SessionDB] 异步应用搜索过滤: keyword='{search_keyword}'")

                    # next_token 分页逻辑
                    if next_token:
                        pagination_model = NextTokenUtils.decode(next_token)
                        if pagination_model.gmt and pagination_model.id:
                            # 按修改时间倒序，如果时间相同则按ID倒序
                            query = query.filter(
                                (SessionModel.gmt_modified < pagination_model.gmt) |
                                ((SessionModel.gmt_modified == pagination_model.gmt) & (SessionModel.id < pagination_model.id))
                            )

                    # 排序 - 优先按修改时间，然后按ID倒序（用于next_token分页）
                    sessions = query.order_by(
                        SessionModel.gmt_modified.desc(),
                        SessionModel.id.desc()
                    ).limit(limit).all()

                    # 创建detached对象避免Session绑定问题
                    detached_sessions = []
                    for session in sessions:
                        detached_session = SessionModel(
                            id=session.id,
                            session_id=session.session_id,
                            ali_uid=session.ali_uid,
                            agent_id=session.agent_id,
                            title=session.title,
                            status=session.status,
                            gmt_create=session.gmt_create,
                            gmt_modified=session.gmt_modified,
                            meta_data=session.meta_data,
                            wy_id=session.wy_id
                        )
                        detached_sessions.append(detached_session)

                    logger.debug(f"[SessionDB] 异步查询Sessions: 返回 {len(detached_sessions)} 个结果")
                    return detached_sessions

            return await asyncio.to_thread(_list_sessions)

        except Exception as e:
            logger.error(f"[SessionDB] 异步查询Sessions失败: {e}")
            raise

    def count_sessions(
        self,
        ali_uid: Optional[str] = None,
        agent_id: Optional[str] = None,
        status_filter: Optional[str] = None,
        search_keyword: Optional[str] = None
    ) -> int:
        """统计Session数量（支持过滤）"""
        try:
            with self.db_manager.get_session() as db_session:
                query = db_session.query(SessionModel)
                
                # 用户过滤
                if ali_uid:
                    query = query.filter(SessionModel.ali_uid == ali_uid)
                
                # Agent过滤
                if agent_id:
                    query = query.filter(SessionModel.agent_id == agent_id)
                
                # 状态过滤
                if status_filter:
                    if status_filter.startswith("!"):
                        # 排除特定状态，如 "!DELETE" 表示排除DELETE状态
                        exclude_status = status_filter[1:]
                        query = query.filter(SessionModel.status != exclude_status)
                        logger.debug(f"[SessionDB] 排除状态过滤统计: status != '{exclude_status}'")
                    else:
                        # 包含特定状态
                        query = query.filter(SessionModel.status == status_filter)
                        logger.debug(f"[SessionDB] 包含状态过滤统计: status == '{status_filter}'")

                # 搜索关键词过滤（标题模糊查询）
                if search_keyword:
                    # 去除前后空格
                    search_keyword = search_keyword.strip()
                    if search_keyword:
                        # 使用LIKE进行模糊查询，支持部分匹配
                        query = query.filter(SessionModel.title.like(f"%{search_keyword}%"))
                        logger.debug(f"[SessionDB] 应用搜索过滤统计: keyword='{search_keyword}'")

                count = query.count()
                logger.debug(f"[SessionDB] Session数量: {count}")
                return count
                
        except Exception as e:
            logger.error(f"[SessionDB] 统计Session数量失败: {e}")
            raise

    async def count_sessions_async(
        self,
        ali_uid: Optional[str] = None,
        agent_id: Optional[str] = None,
        status_filter: Optional[str] = None,
        search_keyword: Optional[str] = None
    ) -> int:
        """异步统计Session数量（支持过滤）"""
        try:
            def _count_sessions():
                # 确保可以访问外部作用域的变量
                nonlocal ali_uid, agent_id, status_filter, search_keyword

                with self.db_manager.get_session() as db_session:
                    query = db_session.query(SessionModel)

                    # 用户过滤
                    if ali_uid:
                        query = query.filter(SessionModel.ali_uid == ali_uid)

                    # Agent过滤
                    if agent_id:
                        query = query.filter(SessionModel.agent_id == agent_id)

                    # 状态过滤
                    if status_filter:
                        if status_filter.startswith("!"):
                            # 排除指定状态
                            exclude_status = status_filter[1:]
                            query = query.filter(SessionModel.status != exclude_status)
                            logger.debug(f"[SessionDB] 异步统计排除状态过滤: exclude_status='{exclude_status}'")
                        else:
                            # 包含指定状态
                            query = query.filter(SessionModel.status == status_filter)
                            logger.debug(f"[SessionDB] 异步统计状态过滤: status='{status_filter}'")

                    # 搜索关键词过滤（标题模糊查询）
                    if search_keyword:
                        # 去除前后空格
                        search_keyword = search_keyword.strip()
                        if search_keyword:
                            # 使用LIKE进行模糊查询，支持部分匹配
                            query = query.filter(SessionModel.title.like(f"%{search_keyword}%"))
                            logger.debug(f"[SessionDB] 异步统计应用搜索过滤: keyword='{search_keyword}'")

                    count = query.count()
                    logger.debug(f"[SessionDB] 异步统计Session数量: {count}")
                    return count

            return await asyncio.to_thread(_count_sessions)

        except Exception as e:
            logger.error(f"[SessionDB] 异步统计Session数量失败: {e}")
            raise

    def get_session_statistics(self) -> Dict[str, Any]:
        """获取Session统计信息"""
        try:
            with self.db_manager.get_session() as db_session:
                # 总数统计
                total_count = db_session.query(SessionModel).count()
                
                # 按状态统计
                status_counts = {}
                for status in SessionStatus:
                    count = db_session.query(SessionModel).filter(
                        SessionModel.status == status
                    ).count()
                    status_counts[status.name.lower()] = count
                
                # 今日创建数量
                today = datetime.now().date()
                today_count = db_session.query(SessionModel).filter(
                    SessionModel.gmt_create >= today
                ).count()
                
                stats = {
                    'total_sessions': total_count,
                    'status_distribution': status_counts,
                    'today_created': today_count
                }
                
                logger.debug(f"[SessionDB] Session统计: {stats}")
                return stats
                
        except Exception as e:
            logger.error(f"[SessionDB] 获取Session统计失败: {e}")
            raise

    def update_session_title(self, session_id: str, new_title: str) -> bool:
        """
        更新会话标题

        Args:
            session_id: 会话ID
            new_title: 新标题

        Returns:
            bool: 是否成功
        """
        try:
            with self.db_manager.get_session() as db_session:
                # 查找会话
                session_model = db_session.query(SessionModel).filter(
                    SessionModel.session_id == session_id
                ).first()

                if not session_model:
                    logger.warning(f"[SessionDB] 会话不存在: session_id={session_id}")
                    return False

                # 更新标题和修改时间
                old_title = session_model.title
                session_model.title = new_title
                session_model.gmt_modified = func.current_timestamp()

                db_session.commit()

                logger.info(f"[SessionDB] 会话标题更新成功: session_id={session_id}, old_title={old_title}, new_title={new_title}")
                return True

        except Exception as e:
            logger.error(f"[SessionDB] 更新会话标题失败: session_id={session_id}, new_title={new_title}, error={e}")
            raise

    async def update_session_title_async(self, session_id: str, new_title: str) -> bool:
        """
        异步更新会话标题

        Args:
            session_id: 会话ID
            new_title: 新标题

        Returns:
            bool: 是否成功
        """
        try:
            def _update_title():
                with self.db_manager.get_session() as db_session:
                    # 查找会话
                    session_model = db_session.query(SessionModel).filter(
                        SessionModel.session_id == session_id
                    ).first()

                    if not session_model:
                        logger.warning(f"[SessionDB] 异步会话不存在: session_id={session_id}")
                        return False

                    # 更新标题和修改时间
                    old_title = session_model.title
                    session_model.title = new_title
                    session_model.gmt_modified = func.current_timestamp()

                    db_session.commit()

                    logger.info(f"[SessionDB] 异步会话标题更新成功: session_id={session_id}, old_title={old_title}, new_title={new_title}")
                    return True

            return await asyncio.to_thread(_update_title)

        except Exception as e:
            logger.error(f"[SessionDB] 异步更新会话标题失败: session_id={session_id}, new_title={new_title}, error={e}")
            raise

    def delete_session(self, session_id: str) -> bool:
        """
        删除会话（软删除）

        Args:
            session_id: 会话ID

        Returns:
            bool: 是否成功
        """
        try:
            with self.db_manager.get_session() as db_session:
                # 查找会话
                session_model = db_session.query(SessionModel).filter(
                    SessionModel.session_id == session_id
                ).first()

                if not session_model:
                    logger.warning(f"[SessionDB] 会话不存在: session_id={session_id}")
                    return False

                # 软删除：更新状态为DELETED
                old_status = session_model.status
                session_model.status = 'DELETED'
                session_model.gmt_modified = func.current_timestamp()

                db_session.commit()

                logger.info(f"[SessionDB] 会话删除成功: session_id={session_id}, old_status={old_status}, title={session_model.title}")
                return True

        except Exception as e:
            logger.error(f"[SessionDB] 删除会话失败: session_id={session_id}, error={e}")
            raise

    async def delete_session_async(self, session_id: str) -> bool:
        """
        异步删除会话（软删除）

        Args:
            session_id: 会话ID

        Returns:
            bool: 是否成功
        """
        try:
            def _delete_session():
                with self.db_manager.get_session() as db_session:
                    # 查找会话
                    session_model = db_session.query(SessionModel).filter(
                        SessionModel.session_id == session_id
                    ).first()

                    if not session_model:
                        logger.warning(f"[SessionDB] 异步会话不存在: session_id={session_id}")
                        return False

                    # 软删除：更新状态为DELETED
                    old_status = session_model.status
                    session_model.status = 'DELETED'
                    session_model.gmt_modified = func.current_timestamp()

                    db_session.commit()

                    logger.info(f"[SessionDB] 异步会话删除成功: session_id={session_id}, old_status={old_status}, title={session_model.title}")
                    return True

            return await asyncio.to_thread(_delete_session)

        except Exception as e:
            logger.error(f"[SessionDB] 异步删除会话失败: session_id={session_id}, error={e}")
            raise

    def hard_delete_session(self, session_id: str) -> bool:
        """
        硬删除会话（物理删除）

        Args:
            session_id: 会话ID

        Returns:
            bool: 是否成功
        """
        try:
            with self.db_manager.get_session() as db_session:
                # 查找会话
                session_model = db_session.query(SessionModel).filter(
                    SessionModel.session_id == session_id
                ).first()

                if not session_model:
                    logger.warning(f"[SessionDB] 会话不存在: session_id={session_id}")
                    return False

                # 物理删除
                title = session_model.title
                db_session.delete(session_model)
                db_session.commit()

                logger.info(f"[SessionDB] 会话硬删除成功: session_id={session_id}, title={title}")
                return True

        except Exception as e:
            logger.error(f"[SessionDB] 硬删除会话失败: session_id={session_id}, error={e}")
            raise

    def restore_session(self, session_id: str) -> bool:
        """
        恢复已删除的会话

        Args:
            session_id: 会话ID

        Returns:
            bool: 是否成功
        """
        try:
            with self.db_manager.get_session() as db_session:
                # 查找已删除的会话
                session_model = db_session.query(SessionModel).filter(
                    SessionModel.session_id == session_id,
                    SessionModel.status == 'DELETED'
                ).first()

                if not session_model:
                    logger.warning(f"[SessionDB] 已删除的会话不存在: session_id={session_id}")
                    return False

                # 恢复状态
                session_model.status = 'ACTIVE'
                session_model.gmt_modified = func.current_timestamp()

                db_session.commit()

                logger.info(f"[SessionDB] 会话恢复成功: session_id={session_id}, title={session_model.title}")
                return True

        except Exception as e:
            logger.error(f"[SessionDB] 恢复会话失败: session_id={session_id}, error={e}")
            raise


# 全局Session数据库服务实例
session_db_service = SessionDatabaseService()