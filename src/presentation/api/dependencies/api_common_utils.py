import json
import traceback

from src.domain.utils.check_utils import ClientException
from loguru import logger
import uuid
from fastapi import Request

OK_STATUS = 200
FAIL_STATUS = 500
CLIENT_ERROR_STATUS = 400

def get_request_id(request: Request = None):
    """
    获取请求ID，支持多种来源

    Args:
        request: FastAPI请求对象，可选

    Returns:
        str: 请求ID
    """
    # 优先级1: 从请求头获取（支持多种格式）
    if request:
        request_id = (request.headers.get('X-Request-ID') or
                     request.headers.get('X-Request-Id') or
                     request.headers.get('x-request-id'))
        if request_id:
            return request_id

        request_id = (request.headers.get('x-acs-req-uuid') or
                     request.headers.get('X-Acs-Req-Uuid') or
                     request.headers.get('X-ACS-REQ-UUID') or
                     request.headers.get('x-acs-request-id'))
        if request_id:
            return request_id

    if request:
        request_id = request.query_params.get('request_id')
        if request_id:
            return request_id

        request_id = request.query_params.get('requestId')
        if request_id:
            return request_id

    # 优先级3: 从当前上下文获取（如果已经在RequestContext中）
    try:
        from ...shared.logging.logger import request_id_var
        current_request_id = request_id_var.get()
        if current_request_id and current_request_id != 'SYSTEM':
            return current_request_id
    except Exception:
        pass

    # 优先级4: 生成新的UUID
    return str(uuid.uuid4())


def get_request_id_dependency(request: Request):
    """
    FastAPI依赖注入函数，用于在路由中获取request_id
    这个函数确保request_id在整个请求生命周期中保持一致

    Args:
        request: FastAPI请求对象

    Returns:
        str: 请求ID
    """
    # 获取request_id
    request_id = get_request_id(request)

    # 确保request_id被设置到上下文中（如果还没有设置的话）
    try:
        from ....shared.logging.logger import request_id_var
        current_context_id = request_id_var.get()
        if current_context_id == 'SYSTEM' or not current_context_id:
            # 如果上下文中还没有设置request_id，则设置它
            request_id_var.set(request_id)
    except Exception as e:
        logger.warning(f"设置request_id到上下文失败: {e}")

    return request_id

#尝试将结果打包为统一的格式
"""系统错误码"""
SYSTEM_ERROR_CODE = 500

def handle_exception(e: Exception, request_id: str) -> dict:
    """处理异常"""
    # 导入自定义异常类
    from src.domain.services.session_service import FileContentTooLargeError
    from src.domain.services.file_service import FileProcessingFailedException, FileProcessingTimeoutException

    if isinstance(e, ClientException):
        return package_api_result(
            code=e.code,
            data=None,
            message=e.message,
            request_id=request_id,
            status=CLIENT_ERROR_STATUS,
            success=False,
        )
    elif isinstance(e, FileContentTooLargeError):
        # 处理文件内容过大异常
        return package_api_result(
            code=SYSTEM_ERROR_CODE,
            data=None,
            message=e.message,
            request_id=request_id,
            status=CLIENT_ERROR_STATUS,
            success=False,
        )
    elif isinstance(e, FileProcessingFailedException):
        # 处理文件解析失败异常
        return package_api_result(
            code="FILE_PROCESSING_FAILED",
            data=None,
            message=e.message,
            request_id=request_id,
            status=CLIENT_ERROR_STATUS,
            success=False,
        )
    elif isinstance(e, FileProcessingTimeoutException):
        # 处理文件处理超时异常
        return package_api_result(
            code="FILE_PROCESSING_TIMEOUT",
            data=None,
            message=e.message,
            request_id=request_id,
            status=CLIENT_ERROR_STATUS,
            success=False,
        )
    else:
        logger.error(f"[API] call error: {e}\n{traceback.format_exc()}")
        return package_api_result(
            code=SYSTEM_ERROR_CODE,
            message="Sorry, something went wrong, please contact the administrator.",
            data=None,
            request_id=request_id,
            status=FAIL_STATUS,
            success=False,
        )


def package_api_result(
    code=None, 
    data=None, 
    message=None, 
    request_id=None, 
    status=None, 
    success=None, 
    dumps=False,
    total_count=0,
    next_token=None
):
    """包装API返回结果
    
    Args:
        code: 错误码，默认为200
        data: 返回数据，默认为空字典
        message: 消息，默认为空字符串
        request_id: 请求ID，默认为空字符串
        status: HTTP状态码，默认为200
        success: 是否成功，默认为False
        dumps: 是否序列化为JSON字符串，默认为False
    
    Returns:
        dict 或 str: 包装后的API结果
    """
    if code is None:
        code = 200
    if data is None:
        data = {}
    if message is None:
        message = ""
    if request_id is None:
        request_id = ""
    if status is None:
        status = OK_STATUS
    if success is None:
        success = True
    if total_count is None:
        total_count = None
    if next_token is None:
        next_token = None
    rlt = {
        "code": code,
        "data": data,
        "message": message,
        "request_id": request_id,
        "status": status,
        "success": success,
        "total_count": total_count,
        "next_token": next_token
    }
    # 利用fastapi内部的json序列化能力，正常情况应该不显示的dumps成json字符串了
    if dumps:
        rlt = json.dumps(rlt, ensure_ascii=False)
    return rlt
