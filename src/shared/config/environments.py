"""
基于Dynaconf的多环境配置管理
支持 daily、pre、prod 三个环境的配置
"""
import os
from enum import Enum
from typing import Dict, Any, Optional
from dynaconf import Dynaconf, LazySettings
from loguru import logger

class Environment(str, Enum):
    """环境枚举"""
    DAILY = "daily"
    PRE = "pre" 
    PROD = "prod"

class EnvironmentManager:
    """环境管理器 - 基于Dynaconf"""
    
    def __init__(self):
        self.current_env = self._get_current_environment()
        self.settings = self._create_settings()
    
    def _get_current_environment(self) -> Environment:
        """获取当前环境"""
        env_str = os.getenv("ENV_FOR_DYNACONF", "daily").lower()
        try:
            return Environment(env_str)
        except ValueError:
            logger.warning(f"未知环境 '{env_str}'，使用默认环境 'daily'")
            return Environment.DAILY
    
    def _create_settings(self) -> LazySettings:
        """创建Dynaconf设置对象"""
        logger.info(f"初始化 {self.current_env.value} 环境配置")
        
        settings = Dynaconf(
            envvar_prefix="ALPHA",  # 环境变量前缀
            settings_files=['properties.toml'],  # 配置文件
            environments=True,  # 启用多环境支持
            env_switcher="ENV_FOR_DYNACONF",  # 环境切换变量
            load_dotenv=True,  # 支持.env文件
            merge_enabled=True,  # 启用配置合并
            # 默认环境是daily
            default_env=self.current_env.value
        )
        
        return settings
    
    def get_config(self) -> LazySettings:
        """获取当前环境配置"""
        return self.settings
    
    def switch_environment(self, env: Environment) -> None:
        """切换环境"""
        os.environ["ENV_FOR_DYNACONF"] = env.value
        self.current_env = env
        # 重新创建settings以应用新环境
        self.settings = self._create_settings()
        logger.info(f"已切换到 {env.value} 环境")
    
    def is_production(self) -> bool:
        """是否为生产环境"""
        return self.current_env == Environment.PROD
    
    def is_development(self) -> bool:
        """是否为开发环境"""
        return self.current_env == Environment.DAILY
    
    def get_environment_info(self) -> Dict[str, Any]:
        """获取环境信息"""
        return {
            "current_environment": self.current_env.value,
            "is_production": self.is_production(),
            "is_development": self.is_development(),
            "config_source": "dynaconf",
            "available_environments": [env.value for env in Environment],
            "settings_files": ["properties.toml"],  # 配置文件列表
            "envvar_prefix": "ALPHA"  # 环境变量前缀
        }
    
    def _decrypt_if_needed(self, value: str) -> str:
        """如果值以enc:开头，则进行解密"""
        if isinstance(value, str) and value.startswith("enc:"):
            try:
                from src.shared.config.key_center_config import kc
                decrypted_value = kc.kc_decrypt(value[4:])  # 去掉enc:前缀
                return decrypted_value
            except Exception as e:
                # 解密失败时打印警告并返回原值
                logger.warning(f"配置解密失败，使用原始值: {e}")
                return value
        return value
    
    def get_config_value(self, key: str, default: Any = None) -> Any:
        """获取配置值并自动解密（如果需要）"""
        value = self.settings.get(key, default)
        if isinstance(value, str):
            return self._decrypt_if_needed(value)
        return value
    
    def list_all_settings(self) -> Dict[str, Any]:
        """列出所有配置项"""
        return dict(self.settings)

# 全局环境管理器实例
env_manager = EnvironmentManager()

# 为了保持向后兼容，提供一个类似Config的接口
class Config:
    """配置访问器 - 兼容原有接口"""
    
    def __init__(self, settings: LazySettings):
        self._settings = settings
    
    def _decrypt_if_needed(self, value: str) -> str:
        """如果值以enc:开头，则进行解密"""
        if isinstance(value, str) and value.startswith("enc:"):
            try:
                from src.shared.config.key_center_config import kc
                decrypted_value = kc.kc_decrypt(value[4:])  # 去掉enc:前缀
                return decrypted_value
            except Exception as e:
                # 解密失败时打印警告并返回原值
                logger.warning(f"配置解密失败，使用原始值: {e}")
                return value
        return value
    
    def _get_config_value(self, key: str, default: any = None) -> any:
        """获取配置值并自动解密（如果需要）"""
        value = self._settings.get(key, default)
        if isinstance(value, str):
            return self._decrypt_if_needed(value)
        return value
    
    @property
    def api_host(self) -> str:
        return self._get_config_value("api_host", "0.0.0.0")
    
    @property
    def api_port(self) -> int:
        return self._get_config_value("api_port", 8000)
    
    @property
    def api_debug(self) -> bool:
        return self._get_config_value("api_debug", False)
    
    @property
    def db_mysql_host(self) -> str:
        return self._get_config_value("db_mysql_host", "localhost")
    
    @property
    def db_mysql_port(self) -> int:
        return self._get_config_value("db_mysql_port", 3306)
    
    @property
    def db_mysql_user(self) -> str:
        return self._get_config_value("db_mysql_user", "root")
    
    @property
    def db_mysql_password(self) -> str:
        return self._get_config_value("db_mysql_password", "")
    
    @property
    def db_mysql_name(self) -> str:
        return self._get_config_value("db_mysql_name", "alpha_service")
    
    @property
    def kc_server(self) -> str:
        return self._get_config_value("kc_server", "")
    
    @property
    def kc_app_code(self) -> str:
        return self._get_config_value("kc_app_code", "")
    
    @property
    def kc_key_name(self) -> str:
        return self._get_config_value("kc_key_name", "")
    
    @property
    def diamond_endpoint(self) -> str:
        return self._get_config_value("diamond_endpoint", "")
    
    @property
    def diamond_data_id(self) -> str:
        return self._get_config_value("diamond_data_id", "")
    
    @property
    def log_level(self) -> str:
        return self._get_config_value("log_level", "INFO")
    
    @property
    def log_file_path(self) -> str:
        return self._get_config_value("log_file_path", "logs/alpha-service.log")
    
    @property
    def mq_enabled(self) -> bool:
        return self._get_config_value("mq_enabled", True)
    
    @property
    def mq_endpoint(self) -> str:
        return self._get_config_value("mq_endpoint", "")
    
    @property
    def mq_user_name(self) -> str:
        return self._get_config_value("mq_user_name", "")
    
    @property
    def mq_password(self) -> str:
        return self._get_config_value("mq_password", "")
    
    @property
    def mq_access_key(self) -> str:
        return self._get_config_value("mq_access_key", "")
    
    @property
    def mq_secret_key(self) -> str:
        return self._get_config_value("mq_secret_key", "")
    
    @property
    def mq_instance_id(self) -> str:
        return self._get_config_value("mq_instance_id", "")
    
    @property
    def mq_topic(self) -> str:
        return self._get_config_value("mq_topic", "waiy_memory")
    
    @property
    def mq_group_id(self) -> str:
        return self._get_config_value("mq_group_id", "GID_WAIY_MESSAGE")
    
    # OSS配置属性
    @property
    def oss_enabled(self) -> bool:
        return self._get_config_value("oss_enabled", True)
    
    @property
    def oss_region(self) -> str:
        return self._get_config_value("oss_region", "cn-hangzhou")
    
    @property
    def oss_bucket_name(self) -> str:
        return self._get_config_value("oss_bucket_name", "alpha-service-oss")
    
    @property
    def oss_endpoint(self) -> str:
        return self._get_config_value("oss_endpoint", "")
    
    @property
    def oss_access_key(self) -> str:
        return self._get_config_value("oss_access_key", "")
    
    @property
    def oss_secret_key(self) -> str:
        return self._get_config_value("oss_secret_key", "")

    # RAG OSS配置属性
    @property
    def rag_oss_enabled(self) -> bool:
        return self._get_config_value("rag_oss_enabled", True)

    @property
    def rag_oss_region(self) -> str:
        return self._get_config_value("rag_oss_region", "cn-hangzhou")

    @property
    def rag_oss_bucket_name(self) -> str:
        return self._get_config_value("rag_oss_bucket_name", "")

    @property
    def rag_oss_endpoint(self) -> str:
        return self._get_config_value("rag_oss_endpoint", "https://oss-cn-hangzhou.aliyuncs.com")

    @property
    def rag_oss_access_key(self) -> str:
        return self._get_config_value("rag_oss_access_key", "")

    @property
    def rag_oss_secret_key(self) -> str:
        return self._get_config_value("rag_oss_secret_key", "")

    # 阿里云无密钥认证配置属性
    @property
    def ram_role_arn(self) -> str:
        return self._get_config_value("ram_role_arn", "")

    @property
    def region_id(self) -> str:
        return self._get_config_value("region_id", "cn-hangzhou")

    @property
    def app_group(self) -> str:
        return self._get_config_value("app_group", "")

    @property
    def akless_env(self) -> str:
        return self._get_config_value("akless_env", "")

    # Redis配置属性
    @property
    def redis_enabled(self) -> bool:
        return self._get_config_value("redis_enabled", True)

    @property
    def redis_host(self) -> str:
        return self._get_config_value("redis_host", "localhost")

    @property
    def redis_port(self) -> int:
        return self._get_config_value("redis_port", 6379)

    @property
    def redis_username(self) -> str:
        return self._get_config_value("redis_username", "")

    @property
    def redis_password(self) -> str:
        return self._get_config_value("redis_password", "")

    @property
    def redis_db(self) -> int:
        return self._get_config_value("redis_db", 0)

    @property
    def redis_max_connections(self) -> int:
        return self._get_config_value("redis_max_connections", 10)

    @property
    def redis_socket_timeout(self) -> int:
        return self._get_config_value("redis_socket_timeout", 5)

    @property
    def redis_socket_connect_timeout(self) -> int:
        return self._get_config_value("redis_socket_connect_timeout", 5)

    # AgentBay配置属性
    @property
    def agentbay_cloud_resource_id(self) -> str:
        return self._get_config_value("agentbay_cloud_resource_id", "")

    @property
    def agentbay_aliuid(self) -> str:
        return self._get_config_value("agentbay_aliuid", "")

# 扩展get_config方法以返回Config包装器
def _get_config_wrapper(self) -> Config:
    """获取当前环境配置 - 兼容原有接口"""
    return Config(self.settings)

# 为EnvironmentManager添加get_config方法
EnvironmentManager.get_config = _get_config_wrapper

# 重新创建全局实例
env_manager = EnvironmentManager() 