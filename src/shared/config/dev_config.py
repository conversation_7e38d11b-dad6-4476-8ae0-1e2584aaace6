# -*- coding: utf-8 -*-
"""
开发模式配置
用于开发和调试时的特殊配置
"""
import os
from typing import Optional

class DevConfig:
    """开发模式配置类"""
    
    # 开发模式开关
    DEV_MODE = os.getenv("DEV_MODE", "true").lower() == "true"
    
    # 固定测试用户配置
    FIXED_TEST_USER = {
        # "ali_uid": ****************,
        # "wy_id": "6752db12224a1119",
        # "end_user_id": "6752db12224a1119",
        "ali_uid": ****************,
        "wy_id": "test_user",
        "end_user_id": "test_user",
        "account_type": "ALIYUN",
        "login_type": "PASSWORD",
        "api_key_id": "test_api_key",
        "extras": {
            "model": "test_model"
        }
    }
    
    # 模拟登录令牌验证
    MOCK_LOGIN_VERIFICATION = os.getenv("MOCK_LOGIN_VERIFICATION", "true").lower() == "true"
    
    # 跳过真实的阿里云API调用
    SKIP_REAL_API_CALLS = os.getenv("SKIP_REAL_API_CALLS", "true").lower() == "true"
    
    # 开发模式日志级别
    DEV_LOG_LEVEL = os.getenv("DEV_LOG_LEVEL", "DEBUG")
    
    @classmethod
    def is_dev_mode(cls) -> bool:
        """检查是否为开发模式"""
        return cls.DEV_MODE
    
    @classmethod
    def should_mock_login(cls) -> bool:
        """检查是否应该模拟登录验证"""
        return cls.DEV_MODE and cls.MOCK_LOGIN_VERIFICATION
    
    @classmethod
    def get_test_user(cls) -> dict:
        """获取固定测试用户信息"""
        return cls.FIXED_TEST_USER.copy()
    
    @classmethod
    def should_skip_api_calls(cls) -> bool:
        """检查是否应该跳过真实API调用"""
        return cls.DEV_MODE and cls.SKIP_REAL_API_CALLS

# 全局开发配置实例
dev_config = DevConfig()

# 便捷函数
def is_dev_mode() -> bool:
    """检查是否为开发模式"""
    return dev_config.is_dev_mode()

def should_mock_login() -> bool:
    """检查是否应该模拟登录验证"""
    return dev_config.should_mock_login()

def get_test_user() -> dict:
    """获取固定测试用户信息"""
    return dev_config.get_test_user()

def should_skip_api_calls() -> bool:
    """检查是否应该跳过真实API调用"""
    return dev_config.should_skip_api_calls()
