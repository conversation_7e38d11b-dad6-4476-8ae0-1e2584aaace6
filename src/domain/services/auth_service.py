#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
统一鉴权服务
"""
import uuid
import hashlib
from typing import Optional, List, Dict, Any, Tuple, Union
from datetime import datetime, timedelta
from fastapi import Request, HTTPException, status, Depends
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from loguru import logger

from ...infrastructure.database.repositories.auth_repository import auth_repository
from ...infrastructure.database.models.auth_models import ResourceType, PermissionType, ShareType
from ...infrastructure.database.models.permission_utils import PermissionUtils, Permissions
from ...popclients.login_verify_client import LoginVerifyClient, LoginVerifyClientError

# 权限参数类型：可以是枚举或字符串
PermissionParam = Union[PermissionType, str]


def _normalize_permission(permission: PermissionParam) -> str:
    """将权限参数标准化为字符串"""
    if isinstance(permission, PermissionType):
        return permission.value
    return permission


class AuthContext:
    """鉴权上下文"""

    def __init__(self, ali_uid: int, wy_id: str, end_user_id: str = None, account_type: str = None):
        self.ali_uid = ali_uid
        self.wy_id = wy_id
        self.end_user_id = end_user_id or wy_id  # 如果没有提供 end_user_id，使用 wy_id 作为默认值
        self.account_type = account_type  # 账户类型，从 user_info 中获取
        self.permissions_cache = {}  # 权限缓存

    @property
    def user_key(self) -> str:
        """用户唯一标识"""
        return f"{self.ali_uid}:{self.wy_id}"

    def to_dict(self) -> Dict[str, Any]:
        return {
            "ali_uid": self.ali_uid,
            "wy_id": self.wy_id,
            "end_user_id": self.end_user_id,
            "account_type": self.account_type,
            "user_key": self.user_key
        }


class AuthResult:
    """鉴权结果"""
    
    def __init__(self, success: bool, message: str = "", permissions: Optional[List[str]] = None):
        self.success = success
        self.message = message
        self.permissions = permissions or []
    
    def __bool__(self):
        return self.success


class AuthService:
    """统一鉴权服务"""
    
    def __init__(self):
        self.auth_repository = auth_repository
        self.security = HTTPBearer(auto_error=False)
        # 延迟初始化登录验证客户端
        self._login_verify_client = None

    @property
    def login_verify_client(self) -> LoginVerifyClient:
        """获取登录验证客户端（延迟初始化）"""
        if self._login_verify_client is None:
            try:
                self._login_verify_client = LoginVerifyClient()
            except LoginVerifyClientError as e:
                logger.warning(f"[AuthService] 无法初始化LoginVerifyClient: {e}")
                logger.info("[AuthService] 将跳过登录令牌验证，使用备用认证方式")
                # 返回一个空的客户端，实际调用时会报错
                self._login_verify_client = None
        return self._login_verify_client
    
    # ==================== 用户认证 ====================

    def create_auth_context(self, ali_uid: int, wy_id: str, end_user_id: str = None, account_type: str = None) -> AuthContext:
        """创建认证上下文"""
        try:
            context = AuthContext(ali_uid=ali_uid, wy_id=wy_id, end_user_id=end_user_id, account_type=account_type)
            return context

        except Exception as e:
            logger.error(f"[AuthService] 创建认证上下文失败: ali_uid={ali_uid}, wy_id={wy_id}, end_user_id={end_user_id}, account_type={account_type}, error={e}")
            raise

    # ==================== HTTP请求认证 ====================

    async def get_current_user(
        self,
        request: Request,
        credentials: Optional[HTTPAuthorizationCredentials] = Depends(HTTPBearer(auto_error=False))
    ) -> Optional[AuthContext]:
        """从HTTP请求中获取当前用户上下文"""
        try:
            # 提取认证参数：支持查询参数、请求头和JSON body
            login_token, login_session_id, region_id, session_token = await self._extract_auth_params(request)
            logger.info(f"[AuthService] 提取到认证参数: login_token={bool(login_token)}, login_session_id={bool(login_session_id)}, region_id={region_id}, session_token={bool(session_token)}")

            # 优先使用session_token，如果存在则忽略其他参数
            if session_token and self.login_verify_client is not None:
                try:
                    logger.info(f"[AuthService] 使用session_token进行认证验证")
                    response = self.login_verify_client.verify_login_token(
                        login_token=session_token
                    )

                    if response.body and response.body.success:
                        # 提取用户信息
                        user_info = self.login_verify_client.get_user_info_from_response(response)
                        if user_info and user_info.get('ali_uid') and user_info.get('wy_id'):
                            ali_uid = user_info['ali_uid']
                            wy_id = user_info['wy_id']
                            end_user_id = user_info.get('end_user_id')
                            account_type = user_info.get('account_type')

                            context = self.create_auth_context(ali_uid, wy_id, end_user_id, account_type)
                            logger.info(f"[AuthService] session_token验证成功: ali_uid={ali_uid}, wy_id={wy_id}, end_user_id={end_user_id}, account_type={account_type}")
                            return context
                        else:
                            logger.warning(f"[AuthService] session_token验证成功但无法提取用户信息: {user_info}")
                    else:
                        error_msg = response.body.message if response.body else "Unknown error"
                        logger.warning(f"[AuthService] session_token验证失败: {error_msg}")

                except LoginVerifyClientError as e:
                    logger.error(f"[AuthService] session_token登录验证客户端错误: {e}")
                except Exception as e:
                    logger.error(f"[AuthService] session_token认证验证失败: {e}")
                    return None
            elif (login_token or login_session_id) and self.login_verify_client is not None:
                try:
                    logger.info(f"[AuthService] 使用login_token/login_session_id进行认证验证")
                    response = self.login_verify_client.verify_login_token(
                        login_session_id=login_session_id,
                        login_token=login_token,
                        region_id=region_id
                    )

                    if response.body and response.body.success:
                        # 提取用户信息
                        user_info = self.login_verify_client.get_user_info_from_response(response)
                        if user_info and user_info.get('ali_uid') and user_info.get('wy_id'):
                            ali_uid = user_info['ali_uid']
                            wy_id = user_info['wy_id']
                            end_user_id = user_info.get('end_user_id')
                            account_type = user_info.get('account_type')

                            context = self.create_auth_context(ali_uid, wy_id, end_user_id, account_type)
                            # logger.info(f"[AuthService] 登录令牌验证成功: ali_uid={ali_uid}, wy_id={wy_id}, end_user_id={end_user_id}, account_type={account_type}")
                            return context
                        else:
                            logger.warning(f"[AuthService] 登录令牌验证成功但无法提取用户信息: {user_info}")
                    else:
                        error_msg = response.body.message if response.body else "Unknown error"
                        logger.warning(f"[AuthService] 登录令牌验证失败: {error_msg}")

                except LoginVerifyClientError as e:
                    logger.error(f"[AuthService] 登录验证客户端错误: {e}")
                except Exception as e:
                    logger.error(f"[AuthService] 登录令牌验证异常: {e}")
            elif (login_token or login_session_id) and self.login_verify_client is None:
                logger.warning("[AuthService] 检测到登录令牌但LoginVerifyClient不可用")
            else:
                logger.debug("[AuthService] 未提供登录令牌或登录会话ID")

        except Exception as e:
            logger.error(f"[AuthService] 用户认证异常: {e}")
            return None

    async def require_auth(
        self,
        request: Request,
        credentials: Optional[HTTPAuthorizationCredentials] = Depends(HTTPBearer(auto_error=False))
    ) -> AuthContext:
        """要求用户认证"""
        # 打印详细的 request 信息
        await self._log_request_details(request, credentials)

        context = await self.get_current_user(request, credentials)
        if not context:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="未认证用户，请提供有效的用户凭证",
                headers={"WWW-Authenticate": "Bearer"}
            )
        return context

    async def _log_request_details(
        self,
        request: Request,
        credentials: Optional[HTTPAuthorizationCredentials] = None
    ):
        """
        打印详细的 request 信息，用于调试

        Args:
            request: FastAPI Request 对象
            credentials: HTTP 认证凭证
        """
        try:
            # 构建详细信息字典
            details = {
                "method": request.method,
                "url": str(request.url),
                "path": request.url.path,
                "client_ip": request.client.host if request.client else 'Unknown'
            }

            # Query Parameters
            if request.query_params:
                query_params = {}
                for key, value in request.query_params.items():
                    # 对敏感信息进行脱敏
                    if key.lower() in ['password', 'token', 'secret', 'key']:
                        query_params[key] = '***'
                    else:
                        query_params[key] = value
                details["query_params"] = query_params
            else:
                details["query_params"] = {}

            # Headers
            if request.headers:
                headers = {}
                for key, value in request.headers.items():
                    # 对敏感信息进行脱敏
                    if key.lower() in ['authorization', 'cookie', 'x-api-key', 'x-auth-token']:
                        headers[key] = '***'
                    else:
                        headers[key] = value
                details["headers"] = headers
            else:
                details["headers"] = {}

            # Body (如果是 POST/PUT 等方法)
            if request.method in ['POST', 'PUT', 'PATCH']:
                try:
                    body = await request.body()
                    if body:
                        # 尝试解析为 JSON
                        try:
                            import json
                            body_json = json.loads(body.decode('utf-8'))
                            # 对敏感字段进行脱敏
                            if isinstance(body_json, dict):
                                sanitized_body = {}
                                for key, value in body_json.items():
                                    if key.lower() in ['password', 'token', 'secret', 'key', 'auth_code']:
                                        sanitized_body[key] = '***'
                                    else:
                                        sanitized_body[key] = value
                                details["body"] = sanitized_body
                            else:
                                details["body"] = body_json
                        except (json.JSONDecodeError, UnicodeDecodeError):
                            # 如果不是 JSON，显示前100个字符
                            body_str = body.decode('utf-8', errors='ignore')[:100]
                            details["body"] = f"{body_str}{'...' if len(body) > 100 else ''}"
                    else:
                        details["body"] = None
                except Exception as body_error:
                    details["body"] = f"无法读取: {body_error}"

            # Path Parameters
            if hasattr(request, 'path_params') and request.path_params:
                details["path_params"] = dict(request.path_params)
            else:
                details["path_params"] = {}

            # 将所有信息打印到一条日志中
            import json
            logger.info(f"[AuthService] Request详情: {json.dumps(details, ensure_ascii=False, separators=(',', ':'))}")

        except Exception as e:
            logger.error(f"[AuthService] 打印 request 详情时出错: {e}")

    def require_resource_permission(
        self,
        resource_type: ResourceType,
        permission: PermissionType,
        resource_id_param: str = "resource_id"
    ):
        """要求特定资源权限的依赖注入"""
        async def check_permission(
            request: Request,
            context: AuthContext = Depends(self.require_auth)
        ) -> AuthContext:
            # 从路径参数或查询参数中获取resource_id
            resource_id = request.path_params.get(resource_id_param) or request.query_params.get(resource_id_param)

            if not resource_id:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"缺少资源ID参数: {resource_id_param}"
                )

            # 执行权限检查
            auth_result = self.check_resource_permission(
                context, resource_type, resource_id, permission
            )

            if not auth_result.success:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail=auth_result.message
                )

            # 将权限信息添加到context中
            context.permissions_cache[f"{resource_type}:{resource_id}"] = auth_result.permissions

            return context

        return check_permission

    def optional_auth(self):
        """可选认证（用于公开资源，但需要区分用户身份的场景）"""
        async def get_optional_user(
            request: Request,
            credentials: Optional[HTTPAuthorizationCredentials] = Depends(HTTPBearer(auto_error=False))
        ) -> Optional[AuthContext]:
            return await self.get_current_user(request, credentials)

        return get_optional_user

    # ==================== 资源鉴权 ====================
    
    def check_resource_permission(
        self,
        context: AuthContext,
        resource_type: ResourceType,
        resource_id: str,
        required_permission: PermissionType
    ) -> AuthResult:
        """检查用户对资源的权限"""
        try:
            # 标准化权限参数
            permission_str = _normalize_permission(required_permission)

            # 获取资源（不包括已删除的）
            resource = self.auth_repository.get_resource(resource_type.value, resource_id)
            if not resource:
                # 检查资源是否存在但已被删除
                deleted_resource = self.auth_repository.get_resource_include_deleted(resource_type.value, resource_id)
                if deleted_resource and deleted_resource.is_deleted:
                    return AuthResult(False, f"资源已被删除: {resource_type}:{resource_id}")
                else:
                    return AuthResult(False, f"资源不存在: {resource_type}:{resource_id}")

            # 检查是否是资源所有者
            if (resource.owner_ali_uid == context.ali_uid and
                resource.owner_wy_id == context.wy_id):
                return AuthResult(True, "资源所有者", Permissions.OWNER)

            # 检查公开资源的读权限
            if resource.is_public and permission_str == Permissions.READ:
                return AuthResult(True, "公开资源", Permissions.PUBLIC)

            # 检查具体权限（暂时简化，后续在这里获取个人团队、组织的权限）
            user_permissions = []

            if permission_str in user_permissions:
                return AuthResult(True, "权限验证通过", user_permissions)

            logger.warning(f"[AuthService] 权限不足: user={context.user_key}, required={permission_str}, has={user_permissions}")
            return AuthResult(False, f"权限不足，需要 {permission_str} 权限")

        except Exception as e:
            logger.error(f"[AuthService] 权限检查异常: user={context.user_key}, error={e}")
            return AuthResult(False, f"权限检查异常: {str(e)}")
    
    def register_resource(
        self,
        context: AuthContext,
        resource_type: ResourceType,
        resource_id: str,
        resource_name: str,
        is_public: bool = False,
        team_id: Optional[int] = None,
        restore_permissions: bool = False,
        restore_shares: bool = False
    ) -> bool:
        """注册资源到鉴权系统"""
        try:
            # 检查资源是否已存在（包括已删除的资源）
            existing_resource = self.auth_repository.get_resource_include_deleted(resource_type.value, resource_id)

            if existing_resource:
                if existing_resource.is_deleted:
                    # 资源之前被删除，现在需要恢复
                    logger.info(f"[AuthService] 发现已删除的资源，准备恢复: {resource_type}:{resource_id}")

                    success = self.auth_repository.restore_resource(
                        resource_type=resource_type.value,
                        resource_id=resource_id,
                        resource_name=resource_name,
                        owner_ali_uid=context.ali_uid if not team_id else None,
                        owner_wy_id=context.wy_id if not team_id else None,
                        team_owner_id=team_id,
                        is_public=is_public
                    )

                    if success:
                        logger.info(f"[AuthService] 资源恢复成功: type={resource_type}, resource_id={resource_id}")

                    return success
                else:
                    # 资源已存在且未删除
                    logger.warning(f"[AuthService] 资源资源已存在且未删除: {resource_type}:{resource_id}")
                    return True

            # 资源不存在，创建新资源记录
            resource = self.auth_repository.create_resource(
                resource_type=resource_type.value,
                resource_id=resource_id,
                resource_name=resource_name,
                owner_ali_uid=context.ali_uid if not team_id else None,
                owner_wy_id=context.wy_id if not team_id else None,
                team_owner_id=team_id,
                is_public=is_public
            )

            logger.info(f"[AuthService] 资源注册成功: id={resource.id}, type={resource_type}, resource_id={resource_id}")
            return True
            
        except Exception as e:
            logger.error(f"[AuthService] 资源注册失败: type={resource_type}, resource_id={resource_id}, error={e}")
            return False

    def unregister_resource(
        self,
        context: AuthContext,
        resource_type: ResourceType,
        resource_id: str
    ) -> bool:
        """从鉴权系统中注销资源"""
        try:
            # 检查资源是否存在
            resource = self.auth_repository.get_resource(resource_type.value, resource_id)
            if not resource:
                logger.warning(f"[AuthService] 资源不存在: {resource_type}:{resource_id}")
                return True  # 资源不存在，认为注销成功

            # 检查权限：只有资源所有者才能注销资源
            if not (resource.owner_ali_uid == context.ali_uid and
                   resource.owner_wy_id == context.wy_id):
                if resource.team_owner_id:
                    # TODO: 还没有团队，先不实现
                    pass
                else:
                    logger.warning(f"[AuthService] 无权限注销资源: user={context.user_key}, resource={resource_type}:{resource_id}")
                    return False

            # 1. 清理资源相关的权限记录
            #self.auth_repository.cleanup_resource_permissions(resource.id)

            # self.auth_repository.cleanup_resource_shares(resource.id)

            # 3. 软删除资源记录，目前只有这一层鉴权
            success = self.auth_repository.delete_resource(resource_type.value, resource_id)

            if success:
                logger.info(f"[AuthService] 资源注销成功: type={resource_type}, resource_id={resource_id}")
            else:
                logger.error(f"[AuthService] 资源注销失败: type={resource_type}, resource_id={resource_id}")

            return success

        except Exception as e:
            logger.error(f"[AuthService] 资源注销异常: type={resource_type}, resource_id={resource_id}, error={e}")
            return False
    
    # ==================== 分享功能 ====================
    
    def create_share(
        self,
        context: AuthContext,
        resource_type: ResourceType,
        resource_id: str,
        share_type: str = ShareType.PRIVATE.value,
        password: Optional[str] = None,
        allowed_permissions: Optional[List[str]] = None,
        max_access_count: Optional[int] = None,
        expires_hours: Optional[int] = None
    ) -> Tuple[bool, Optional[str], str]:
        """创建分享链接"""
        try:
            # 检查用户是否有分享权限
            auth_result = self.check_resource_permission(context, resource_type, resource_id, PermissionType.SHARE)
            if not auth_result.success:
                return False, None, auth_result.message
            
            # 获取资源
            resource = self.auth_repository.get_resource(resource_type.value, resource_id)
            if not resource:
                return False, None, "资源不存在"
            
            # 生成分享码
            share_code = self._generate_share_code(resource_type.value, resource_id)
            
            # 计算过期时间
            expires_at = None
            if expires_hours:
                expires_at = datetime.utcnow() + timedelta(hours=expires_hours)
            
            # 创建分享记录
            share = self.auth_repository.create_share(
                resource_id=resource.id,
                creator_ali_uid=context.ali_uid,
                creator_wy_id=context.wy_id,
                share_code=share_code,
                share_type=share_type,
                password=password,
                allowed_permissions=allowed_permissions or Permissions.READ_ONLY,
                max_access_count=max_access_count,
                expires_at=expires_at
            )
            
            logger.info(f"[AuthService] 创建分享成功: share_id={share.id}, code={share_code}")
            return True, share_code, "分享创建成功"
            
        except Exception as e:
            logger.error(f"[AuthService] 创建分享失败: resource={resource_type}:{resource_id}, error={e}")
            return False, None, f"创建分享失败: {str(e)}"
    
    def access_share(
        self,
        share_code: str,
        password: Optional[str] = None,
        context: Optional[AuthContext] = None
    ) -> Tuple[bool, Optional[Dict[str, Any]], str]:
        """访问分享链接"""
        try:
            # 获取分享信息
            share = self.auth_repository.get_share_by_code(share_code)
            if not share:
                return False, None, "分享不存在或已过期"
            
            # 检查访问次数限制
            if share.max_access_count and share.current_access_count >= share.max_access_count:
                return False, None, "分享访问次数已达上限"
            
            # 检查密码
            if share.password and share.password != password:
                return False, None, "分享密码错误"
            
            # 构建分享信息
            share_info = {
                "resource_type": share.resource.resource_type,
                "resource_id": share.resource.resource_id,
                "resource_name": share.resource.resource_name,
                "allowed_permissions": share.allowed_permissions.split(',') if share.allowed_permissions else Permissions.READ_ONLY,
                "creator": {
                    "id": share.creator.id,
                    "username": share.creator.username,
                    "ali_uid": share.creator.ali_uid,
                    "wy_id": share.creator.wy_id
                },
                "expires_at": share.expires_at.isoformat() if share.expires_at else None
            }
            
            # 记录访问日志（这里简化处理，实际应该在repository中实现）
            # TODO: 实现访问日志记录
            
            logger.info(f"[AuthService] 分享访问成功: code={share_code}, resource={share.resource.resource_type}:{share.resource.resource_id}")
            return True, share_info, "访问成功"
            
        except Exception as e:
            logger.error(f"[AuthService] 分享访问失败: code={share_code}, error={e}")
            return False, None, f"访问失败: {str(e)}"
    
    # ==================== 团队管理 以后可能会用到，预留====================
    
    def create_team(self, context: AuthContext, name: str, description: Optional[str] = None) -> Tuple[bool, Optional[int], str]:
        """创建团队"""
        try:
            team = self.auth_repository.create_team(name, context.ali_uid, context.wy_id, description)
            logger.info(f"[AuthService] 创建团队成功: team_id={team.id}, name={name}")
            return True, team.id, "团队创建成功"
            
        except Exception as e:
            logger.error(f"[AuthService] 创建团队失败: name={name}, error={e}")
            return False, None, f"创建团队失败: {str(e)}"
    
    def get_user_teams(self, context: AuthContext) -> List[Dict[str, Any]]:
        """获取用户所在的团队"""
        try:
            teams = self.auth_repository.get_user_teams(context.ali_uid, context.wy_id)
            return [
                {
                    "id": team.id,
                    "name": team.name,
                    "description": team.description,
                    "creator_id": team.creator_id,
                    "gmt_created": team.gmt_created.isoformat() if team.gmt_created else None
                }
                for team in teams
            ]
            
        except Exception as e:
            logger.error(f"[AuthService] 获取用户团队失败: user={context.user_key}, error={e}")
            return []
    
    # ==================== 私有方法 ====================

    def _get_raw_login_token(self, request: Request) -> Optional[str]:
        """
        获取原始LoginToken，不进行URL解码，避免+被转换为空格

        Args:
            request: FastAPI Request对象

        Returns:
            str: 原始的LoginToken，如果不存在则返回None
        """
        # 首先尝试从原始查询字符串中提取
        query_string = str(request.url.query)
        if query_string:
            # 支持大小写驼峰命名
            for param_name in ["LoginToken", "loginToken"]:
                param_prefix = f"{param_name}="
                if param_prefix in query_string:
                    for param in query_string.split('&'):
                        if param.startswith(param_prefix):
                            raw_token = param.split('=', 1)[1]
                            logger.debug(f"从原始查询字符串提取{param_name}: {raw_token}")
                            return raw_token

        # 如果没找到，返回None（后续会尝试其他方式）
        return None

    async def _extract_auth_params(self, request: Request) -> tuple[Optional[str], Optional[str], Optional[str], Optional[str]]:
        """
        从请求中提取认证参数
        支持三种方式：查询参数、请求头、JSON body
        优先级：查询参数 > 请求头 > JSON body

        Returns:
            tuple: (login_token, login_session_id, region_id, session_token)
        """
        # 方式1: 从查询参数提取（兼容大小写驼峰命名）
        # 如果原始提取失败，回退到默认方式
        login_token = (request.query_params.get("LoginToken") or
                      request.query_params.get("loginToken"))
        if login_token and ' ' in login_token:
            login_token = login_token.replace(' ', '+')

        login_session_id = (request.query_params.get("LoginSessionId") or
                            request.query_params.get("loginSessionId") or
                            request.query_params.get("LoginSession") or
                            request.query_params.get("loginSession"))
        region_id = (request.query_params.get("RegionId") or
                    request.query_params.get("regionId"))
        session_token = (request.query_params.get("SessionToken") or
                        request.query_params.get("sessionToken"))
        if session_token and ' ' in session_token:
            session_token = session_token.replace(' ', '+')

        # 方式2: 从请求头提取（如果查询参数中没有，兼容大小写驼峰命名）
        if not login_token:
            login_token = (request.headers.get("X-Login-Token") or
                          request.headers.get("X-login-token"))
        if not login_session_id:
            login_session_id = (request.headers.get("X-Login-Session-Id") or
                               request.headers.get("X-login-session-id"))
        if not region_id:
            region_id = (request.headers.get("X-Region-Id") or
                        request.headers.get("X-region-id"))
        if not session_token:
            session_token = (request.headers.get("X-Session-Token") or
                           request.headers.get("X-session-token"))

        # 方式3: 从JSON body提取（如果前两种方式都没有）
        if not any([login_token, login_session_id, region_id, session_token]):
            try:
                # 尝试从缓存的JSON body中提取（需要BodyCacheMiddleware支持）
                json_data = getattr(request.state, 'json_body', None)

                if json_data:
                    # 从JSON中提取认证参数（兼容大小写驼峰命名）
                    if not login_token:
                        login_token = (json_data.get("LoginToken") or
                                      json_data.get("loginToken"))
                    if not login_session_id:
                        login_session_id = (json_data.get("LoginSessionId") or
                                           json_data.get("loginSessionId"))
                    if not region_id:
                        region_id = (json_data.get("RegionId") or
                                    json_data.get("regionId"))
                    if not session_token:
                        session_token = (json_data.get("SessionToken") or
                                       json_data.get("sessionToken"))

                    logger.debug(f"[AuthService] 从JSON body提取认证参数: login_token={bool(login_token)}, login_session_id={bool(login_session_id)}, region_id={region_id}, session_token={bool(session_token)}")
                else:
                    logger.debug("[AuthService] 未找到缓存的JSON body，可能需要添加BodyCacheMiddleware")

            except Exception as e:
                logger.warning(f"[AuthService] 从JSON body提取认证参数失败: {e}")

        return login_token, login_session_id, region_id, session_token

    def _is_resource_owner(self, user_id: int, resource_id: int) -> bool:
        """检查用户是否是资源所有者"""
        return self.auth_repository.check_resource_ownership(user_id, resource_id)
    
    def _get_user_resource_permissions(self, user_id: int, resource_id: int) -> List[str]:
        """获取用户对资源的权限列表"""
        return self.auth_repository.get_user_permissions(user_id, resource_id)
    
    def _generate_share_code(self, resource_type: str, resource_id: str) -> str:
        """生成分享码"""
        # 使用UUID + 资源信息生成唯一分享码
        unique_str = f"{resource_type}:{resource_id}:{uuid.uuid4().hex}:{datetime.utcnow().timestamp()}"
        return hashlib.md5(unique_str.encode()).hexdigest()[:16]
    
    # ==================== 装饰器支持 ====================
    
    def require_permission(self, resource_type: ResourceType, permission: PermissionType, resource_id_param: str = "resource_id"):
        """权限检查装饰器

        Args:
            resource_type: 资源类型
            permission: 所需权限
            resource_id_param: 资源ID参数名（默认为'resource_id'）
        """
        def decorator(func):
            from functools import wraps

            @wraps(func)
            def wrapper(*args, **kwargs):
                # 1. 提取用户上下文
                context = None

                # 从kwargs中查找context
                if 'context' in kwargs:
                    context = kwargs['context']
                # 从args中查找context（通常是第一个参数）
                elif args and isinstance(args[0], AuthContext):
                    context = args[0]
                # 从kwargs中查找用户信息
                elif 'ali_uid' in kwargs and 'wy_id' in kwargs:
                    context = AuthContext(
                        ali_uid=kwargs['ali_uid'],
                        wy_id=kwargs['wy_id'],
                        end_user_id=kwargs.get('end_user_id'),
                        account_type=kwargs.get('account_type')
                    )

                if not context:
                    raise ValueError("缺少用户认证上下文，请确保传入context参数或ali_uid+wy_id参数")

                # 2. 提取资源ID
                resource_id = None

                # 从kwargs中查找
                if resource_id_param in kwargs:
                    resource_id = kwargs[resource_id_param]
                # 从函数参数名中查找（通过inspect）
                else:
                    import inspect
                    sig = inspect.signature(func)
                    param_names = list(sig.parameters.keys())

                    # 查找可能的资源ID参数
                    possible_params = [resource_id_param, 'file_id', 'session_id', 'kb_id', 'id']
                    for param_name in possible_params:
                        if param_name in param_names:
                            param_index = param_names.index(param_name)
                            if param_index < len(args):
                                resource_id = args[param_index]
                                break
                            elif param_name in kwargs:
                                resource_id = kwargs[param_name]
                                break

                if not resource_id:
                    raise ValueError(f"缺少资源ID参数 '{resource_id_param}'，请确保函数参数中包含资源ID")

                # 3. 执行权限检查
                auth_result = self.check_resource_permission(
                    context=context,
                    resource_type=resource_type,
                    resource_id=str(resource_id),
                    required_permission=permission
                )

                if not auth_result.success:
                    logger.warning(f"权限检查失败: user={context.user_key}, resource={resource_type}:{resource_id}, permission={permission}, reason={auth_result.message}")
                    raise PermissionError(auth_result.message)

                # 4. 将权限信息添加到kwargs中（仅当函数支持**kwargs时）
                import inspect
                sig = inspect.signature(func)

                # 检查函数是否接受**kwargs
                accepts_kwargs = any(param.kind == param.VAR_KEYWORD for param in sig.parameters.values())

                if accepts_kwargs:
                    kwargs['_auth_result'] = auth_result
                    kwargs['_auth_permissions'] = auth_result.permissions

                logger.debug(f"权限检查通过: user={context.user_key}, resource={resource_type}:{resource_id}, permission={permission}")

                return func(*args, **kwargs)
            return wrapper
        return decorator


# 创建全局实例
auth_service = AuthService()


# ==================== 便捷的依赖注入函数 ====================

async def get_current_user(request: Request) -> Optional[AuthContext]:
    """获取当前用户（可选）"""
    return await auth_service.get_current_user(request)

async def require_auth(request: Request) -> AuthContext:
    """要求用户认证"""
    return await auth_service.require_auth(request)

def require_file_read_permission(resource_id_param: str = "file_id"):
    """要求文件读权限

    Args:
        resource_id_param: 资源ID参数名，默认为'file_id'
    """
    return auth_service.require_permission(ResourceType.FILE, PermissionType.READ, resource_id_param)

def require_file_write_permission(resource_id_param: str = "file_id"):
    """要求文件写权限

    Args:
        resource_id_param: 资源ID参数名，默认为'file_id'
    """
    return auth_service.require_permission(ResourceType.FILE, PermissionType.WRITE, resource_id_param)

def require_file_delete_permission(resource_id_param: str = "file_id"):
    """要求文件删除权限

    Args:
        resource_id_param: 资源ID参数名，默认为'file_id'
    """
    return auth_service.require_permission(ResourceType.FILE, PermissionType.DELETE, resource_id_param)

def require_session_read_permission(resource_id_param: str = "session_id"):
    """要求会话读权限

    Args:
        resource_id_param: 资源ID参数名，默认为'session_id'
    """
    return auth_service.require_permission(ResourceType.SESSION, PermissionType.READ, resource_id_param)

def require_session_write_permission(resource_id_param: str = "session_id"):
    """要求会话写权限

    Args:
        resource_id_param: 资源ID参数名，默认为'session_id'
    """
    return auth_service.require_permission(ResourceType.SESSION, PermissionType.WRITE, resource_id_param)

def require_knowledge_base_read_permission(resource_id_param: str = "kb_id"):
    """要求知识库读权限

    Args:
        resource_id_param: 资源ID参数名，默认为'kb_id'
    """
    return auth_service.require_permission(ResourceType.KNOWLEDGE_BASE, PermissionType.READ, resource_id_param)

def require_knowledge_base_write_permission(resource_id_param: str = "kb_id"):
    """要求知识库写权限

    Args:
        resource_id_param: 资源ID参数名，默认为'kb_id'
    """
    return auth_service.require_permission(ResourceType.KNOWLEDGE_BASE, PermissionType.WRITE, resource_id_param)

def require_artifact_read_permission():
    """要求制品读权限"""
    return auth_service.require_resource_permission(ResourceType.FILE, PermissionType.READ, "artifact_id")

def require_knowledge_read_permission():
    """要求知识库读权限"""
    return auth_service.require_resource_permission(ResourceType.KNOWLEDGE_BASE, PermissionType.READ, "knowledge_id")


# ==================== 重新导出常用类型和常量 ====================
# 这样用户只需要 from auth_service import ... 就能获得所有需要的类型

# 导出权限相关类型
__all__ = [
    # 核心服务和类型
    'auth_service',
    'AuthContext',
    'AuthResult',
    'PermissionParam',

    # 枚举类型
    'PermissionType',
    'ResourceType',
    'ShareType',

    # 权限常量和工具
    'Permissions',
    'PermissionUtils',

    # 便捷函数
    'get_current_user',
    'require_auth',
    'require_file_read_permission',
    'require_file_write_permission',
    'require_file_delete_permission',
    'require_session_read_permission',
    'require_session_write_permission',
    'require_knowledge_base_read_permission',
    'require_knowledge_base_write_permission',
    'require_artifact_read_permission',
    'require_knowledge_read_permission',
]
