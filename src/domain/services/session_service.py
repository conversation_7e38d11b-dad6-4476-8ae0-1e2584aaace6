#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
会话业务服务
"""
from typing import Optional, List, Dict, Any, Tuple, AsyncGenerator
from loguru import logger
import uuid
import asyncio
import json
import time
from datetime import datetime

from ...infrastructure.database.repositories.session_repository import session_db_service
from ...infrastructure.database.models.session_models import SessionModel
from .auth_service import AuthContext
from .file_service import file_service
from ...popclients.waiy_infra_client import create_waiy_infra_client, WaiyInfraClientError
from ...application.api_models import ResourceType, SessionResource
from .auth_service import auth_service
from .knowledge_service import knowledgebase_service
from ...shared.config.environments import env_manager
from ...infrastructure.database.models.auth_models import PermissionType
from ...popclients.appstream_inner_client import get_appstream_inner_client, AppStreamInnerClientError
from ...infrastructure.redis import RedisClient
from memory.message import Message
from memory.events import EventType



class SessionPermissionError(Exception):
    """会话权限异常"""
    def __init__(self, message: str, session_id: str = None):
        self.message = message
        self.session_id = session_id
        super().__init__(message)

    def __str__(self):
        if self.session_id:
            return f"会话权限错误 ({self.session_id}): {self.message}"
        return f"会话权限错误: {self.message}"


class FileContentTooLargeError(Exception):
    """文件内容过大异常"""
    def __init__(self, message: str = "文件内容过多，请精简一下", total_length: int = None, limit: int = None):
        self.message = message
        self.total_length = total_length
        self.limit = limit
        super().__init__(message)

    def __str__(self):
        if self.total_length and self.limit:
            return f"{self.message} (当前: {self.total_length} 字符, 限制: {self.limit} 字符)"
        return self.message
from src.infrastructure.memory.memory_sdk import memory_sdk
from .message_processor import MessageProcessor
from .sse_manager import SSEManager


class SSEStreamManager:
    """SSE流管理器 - 简化版本，直接基于Memory SDK事件"""
    
    def __init__(self):
        self.sse_connections: Dict[str, asyncio.Queue] = {}
        self.heartbeat_tasks: Dict[str, asyncio.Task] = {}
    
    def create_connection(self, session_id: str) -> asyncio.Queue:
        """为session创建消息队列"""
        message_queue = asyncio.Queue()
        self.sse_connections[session_id] = message_queue
        return message_queue
    
    def close_connection(self, session_id: str):
        """关闭session连接"""
        if session_id in self.sse_connections:
            del self.sse_connections[session_id]
        
        if session_id in self.heartbeat_tasks:
            task = self.heartbeat_tasks[session_id]
            if not task.done():
                task.cancel()
            del self.heartbeat_tasks[session_id]
    
    def close_session_connection(self, session_id: str):
        """关闭session连接 - 兼容MessageProcessor接口"""
        self.close_connection(session_id)
    
    async def push_event(self, session_id: str, event_data: Dict[str, Any]):
        """推送事件到SSE连接"""
        if session_id in self.sse_connections:
            try:
                await self.sse_connections[session_id].put(event_data)
                logger.debug(f"[SSEStreamManager] 事件推送成功: session_id={session_id}")
            except Exception as e:
                logger.error(f"[SSEStreamManager] 事件推送失败: session_id={session_id}, error={e}")
    
    async def push_to_sse(self, session_id: str, round_id: str, data: Dict[str, Any], event_type: str = "message"):
        """推送数据到SSE连接 - 兼容MessageProcessor接口"""
        event_data = {
            "eventType": event_type,
            "data": data
        }
        logger.info(f"消息下发：{event_data}")
        await self.push_event(session_id, event_data)
    
    async def heartbeat_loop(self, session_id: str, message_queue: asyncio.Queue):
        """心跳循环"""
        try:
            while True:
                await asyncio.sleep(3)  # 每3秒发送一次心跳
                if session_id not in self.sse_connections:
                    break
                
                heartbeat_data = {
                    "type": "heartbeat",
                    "data": {"timestamp": datetime.now().isoformat()}
                }
                await message_queue.put(heartbeat_data)
        except asyncio.CancelledError:
            logger.info(f"[SSEStreamManager] 心跳任务被取消: session_id={session_id}")
        except Exception as e:
            logger.error(f"[SSEStreamManager] 心跳异常: session_id={session_id}, error={e}")


class SessionListParams:
    """会话列表查询参数"""
    
    def __init__(
        self,
        page_size: int = 20,
        next_token: Optional[str] = None,
        order_by: str = "gmt_modified",
        order_direction: str = "desc",
        search_keyword: Optional[str] = None,
        agent_id: Optional[str] = None,
        session_id: Optional[str] = None
    ):
        self.page_size = min(max(1, page_size), 100)  # 限制页面大小在1-100之间
        self.next_token = next_token
        self.order_by = order_by if order_by in ["gmt_create", "gmt_modified"] else "gmt_modified"
        self.order_direction = order_direction if order_direction in ["asc", "desc"] else "desc"
        self.search_keyword = search_keyword
        self.agent_id = agent_id
        self.session_id = session_id
    
    @property
    def limit(self) -> int:
        """获取限制数量"""
        return self.page_size


class SessionInfo:
    """会话信息模型"""

    def __init__(self, session_model, is_in_kb: bool = False):
        self.session_id = session_model.session_id
        self.title = session_model.title or "无标题"
        self.agent_id = session_model.agent_id
        self.ali_uid = session_model.ali_uid
        self.wy_id = session_model.wy_id
        self.status = self._format_status(session_model.status)
        self.gmt_create = session_model.gmt_create
        self.gmt_modified = session_model.gmt_modified
        self.metadata = session_model.meta_data or {}
        self.is_in_kb = is_in_kb  # 是否属于知识库

        # 从metadata中提取额外信息
        self.total_rounds = self._extract_total_rounds()
        self.last_user_prompt = self._extract_last_user_prompt()
    
    def _format_status(self, status) -> str:
        """格式化状态"""
        # 现在状态直接是字符串
        return str(status).lower() if status else 'unknown'
    
    def _extract_total_rounds(self) -> int:
        """从metadata中提取总轮次数"""
        return self.metadata.get("total_rounds", 0)
    
    def _extract_last_user_prompt(self) -> str:
        """从metadata中提取最后一次用户输入"""
        return self.metadata.get("last_user_prompt", "")[:100]  # 限制长度
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "sessionId": self.session_id,
            "title": self.title,
            "agentId": self.agent_id,
            "aliUid": self.ali_uid,
            "wyId": self.wy_id,
            "status": self.status,
            "gmtCreate": self.gmt_create.isoformat() if self.gmt_create else None,
            "gmtModified": self.gmt_modified.isoformat() if self.gmt_modified else None,
            "totalRounds": self.total_rounds,
            "lastUserPrompt": self.last_user_prompt,
            "metadata": self.metadata,
            "isInKb": self.is_in_kb
        }


class SessionListResult:
    """会话列表结果"""
    
    def __init__(
        self,
        sessions: List[SessionInfo],
        page_size: int,
        total_count: int,
        next_token: Optional[str] = None,
        has_more: bool = False
    ):
        self.sessions = sessions
        self.page_size = page_size
        self.total_count = total_count
        self.next_token = next_token
        self.has_more = has_more
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        result = {
            "sessions": [session.to_dict() for session in self.sessions],
            "page_size": self.page_size,
            "total_count": self.total_count,
            "has_more": self.has_more
        }
        if self.next_token:
            result["next_token"] = self.next_token
        return result


class SessionService:
    """会话业务服务"""
    
    def __init__(self):
        self.session_db_service = session_db_service
        self.memory_sdk = memory_sdk  # 使用全局MemorySDK实例
        self.sse_stream_manager = SSEManager()
        self.message_processor = MessageProcessor()
        self.message_processor.set_sse_manager(self.sse_stream_manager)
        # 设置 SSEManager 的 session_loader
        self.sse_stream_manager.set_session_loader(self)
        # 将配置好的MessageProcessor设置到MemorySDK中
        self.memory_sdk.set_message_processor(self.message_processor)
        self._initialized = False
        self._cleanup_task = None
    
    async def initialize(self):
        """初始化会话服务"""
        if self._initialized:
            logger.warning("[SessionService] 服务已经初始化，跳过重复初始化")
            return
            
        try:
            logger.info("[SessionService] 开始初始化会话服务...")

            # MemorySDK 现在直接调用 MessageProcessor，无需注册回调
            logger.info("[SessionService] MemorySDK 直接调用模式已启用")

            self._initialized = True
            logger.info("[SessionService] 会话服务初始化完成")
            
        except Exception as e:
            logger.error(f"[SessionService] 初始化失败: {e}")
            raise
    
    async def shutdown(self):
        """关闭会话服务，清理资源"""
        try:
            logger.info("[SessionService] 开始关闭会话服务...")
            
            # 1. 取消后台清理任务
            if self._cleanup_task and not self._cleanup_task.done():
                self._cleanup_task.cancel()
                try:
                    await self._cleanup_task
                except asyncio.CancelledError:
                    pass
                logger.info("[SessionService] 后台清理任务已停止")
            
            # 2. 清理所有SSE连接
            active_sessions = list(self.sse_stream_manager.sse_connections.keys())
            for session_id in active_sessions:
                self.sse_stream_manager.close_session_connection(session_id)
            logger.info(f"[SessionService] 已清理 {len(active_sessions)} 个SSE连接")

            self._initialized = False
            logger.info("[SessionService] 会话服务关闭完成")
            
        except Exception as e:
            logger.error(f"[SessionService] 关闭会话服务异常: {e}")

    async def get_user_sessions(
        self,
        context: AuthContext,
        params: SessionListParams,
        kb_id: Optional[str] = None
    ) -> SessionListResult:
        """
        获取用户会话列表
        
        Args:
            context: 认证上下文
            params: 查询参数
            kb_id: 知识库id
            
        Returns:
            SessionListResult: 会话列表结果
        """
        try:
            logger.info(f"[SessionService] 获取用户会话列表: user={context.user_key}, page_size={params.page_size}, next_token={params.next_token}")
            
            # 查询会话列表（获取limit+1条用于判断是否还有更多数据）
            # 默认排除状态为DELETE的会话
            session_models = await self.session_db_service.list_sessions_async(
                limit=params.limit + 1,
                ali_uid=str(context.ali_uid),
                agent_id=params.agent_id,
                next_token=params.next_token,
                search_keyword=params.search_keyword,
                status_filter="!DELETED"  # 排除已删除的会话
            )
            
            # 处理分页逻辑
            has_more = len(session_models) > params.limit
            if has_more:
                session_models = session_models[:-1]  # 移除多余的一条
            
            # 创建SessionInfo对象，并判断是否属于知识库
            sessions = []
            if kb_id and session_models:
                # 如果提供了kb_id，则批量判断会话是否属于知识库
                session_id_list = [model.session_id for model in session_models]
                kb_session_results = knowledgebase_service.is_knowledge_base_session(
                    kb_id=kb_id,
                    session_id_list=session_id_list
                )

                # 创建SessionInfo对象，设置is_in_kb字段
                for model in session_models:
                    is_in_kb = kb_session_results.get(model.session_id, False)
                    sessions.append(SessionInfo(model, is_in_kb=is_in_kb))
            else:
                # 如果没有提供kb_id，则所有会话的is_in_kb都为False
                sessions = [SessionInfo(model, is_in_kb=False) for model in session_models]

            # 获取总数（排除已删除的会话）
            total_count = await self.session_db_service.count_sessions_async(
                ali_uid=str(context.ali_uid),
                agent_id=params.agent_id,
                search_keyword=params.search_keyword,
                status_filter="!DELETE"  # 排除已删除的会话
            )
            
            # 生成next_token
            next_token = None
            if has_more and sessions:
                from src.domain.utils.next_token_utils import NextTokenUtils
                last_session = session_models[-1]
                next_token = NextTokenUtils.encode(
                    id=last_session.id,
                    date=last_session.gmt_modified
                )
            
            logger.info(f"[SessionService] 查询完成: user={context.user_key}, 返回 {len(sessions)} 个会话, 总数 {total_count}, has_more={has_more}")
            
            return SessionListResult(
                sessions=sessions,
                page_size=params.page_size,
                total_count=total_count,
                next_token=next_token,
                has_more=has_more
            )
            
        except Exception as e:
            logger.error(f"[SessionService] 获取用户会话列表失败: user={context.user_key}, error={e}")
            raise
    
    async def get_session_with_permission_check_async(
        self,
        context: AuthContext,
        session_id: str,
        required_permission: PermissionType,
    ) -> SessionModel:
        """
        异步获取会话数据，带权限校验
        
        Args:
            context: 认证上下文
            session_id: 会话ID
            required_permission: 所需权限，默认为read
            
        Returns:
            SessionModel: 会话数据库模型
            
        Raises:
            SessionPermissionError: 权限不足时抛出
            ValueError: 会话不存在时抛出
        """
        try:
            # 1. 异步检查权限
            auth_result = await asyncio.to_thread(
                auth_service.check_resource_permission,
                context=context,
                resource_type=ResourceType.SESSION,
                resource_id=session_id,
                required_permission=required_permission
            )

            if not auth_result.success:
                logger.warning(f"[SessionService] 会话权限校验失败: user={context.user_key}, session_id={session_id}, reason={auth_result.message}")
                raise SessionPermissionError(auth_result.message, session_id)

            # 2. 异步获取会话数据
            session_model = await self.session_db_service.get_session_by_id_async(session_id)

            if not session_model:
                logger.warning(f"[SessionService] 会话不存在: session_id={session_id}")
                raise ValueError(f"会话不存在: {session_id}")

            return session_model

        except SessionPermissionError:
            raise
        except ValueError:
            raise
        except Exception as e:
            logger.error(f"[SessionService] 异步会话权限校验异常: user={context.user_key}, session_id={session_id}, error={e}")
            raise SessionPermissionError(f"权限校验异常: {str(e)}", session_id)

    def get_session_with_permission_check(
        self,
        context: AuthContext,
        session_id: str,
        required_permission: PermissionType,
    ) -> SessionModel:
        """
        获取会话数据，带权限校验
        
        Args:
            context: 认证上下文
            session_id: 会话ID
            required_permission: 所需权限，默认为read
            
        Returns:
            SessionModel: 会话数据库模型
            
        Raises:
            SessionPermissionError: 权限不足时抛出
            ValueError: 会话不存在时抛出
        """
        try:
            logger.info(f"[SessionService] 校验会话权限: user={context.user_key}, session_id={session_id}, permission={required_permission}")
            
            # 1. 先检查权限
            auth_result = auth_service.check_resource_permission(
                context=context,
                resource_type=ResourceType.SESSION,
                resource_id=session_id,
                required_permission=required_permission
            )
            
            if not auth_result.success:
                logger.warning(f"[SessionService] 会话权限校验失败: user={context.user_key}, session_id={session_id}, reason={auth_result.message}")
                raise SessionPermissionError(auth_result.message, session_id)
            
            # 2. 权限检查通过后，获取会话数据
            session_model = self.session_db_service.get_session_by_id(session_id)
            
            if not session_model:
                logger.warning(f"[SessionService] 会话不存在: session_id={session_id}")
                raise ValueError(f"会话不存在: {session_id}")
            
            logger.info(f"[SessionService] 会话权限校验成功: user={context.user_key}, session_id={session_id}, permissions={auth_result.permissions}")
            return session_model
            
        except SessionPermissionError:
            raise
        except ValueError:
            raise
        except Exception as e:
            logger.error(f"[SessionService] 会话权限校验异常: user={context.user_key}, session_id={session_id}, error={e}")
            raise SessionPermissionError(f"权限校验异常: {str(e)}", session_id)

    def get_sessions_by_ids(self, session_ids: List[str]) -> List[SessionInfo]:
        """
        根据会话ID列表批量获取会话信息（不进行权限检查）

        Args:
            session_ids: 会话ID列表

        Returns:
            List[SessionInfo]: 会话信息列表
        """
        try:
            if not session_ids:
                logger.debug("[SessionService] 批量获取会话: 空列表")
                return []

            logger.info(f"[SessionService] 批量获取会话: 查询{len(session_ids)}个会话")

            # 从数据库批量查询会话
            session_models = self.session_db_service.get_sessions_by_ids(session_ids)

            # 转换为SessionInfo对象，批量获取时默认不判断知识库关系
            sessions = [SessionInfo(session_model, is_in_kb=False) for session_model in session_models]

            logger.info(f"[SessionService] 批量获取会话完成: 查询{len(session_ids)}个, 返回{len(sessions)}个")
            return sessions

        except Exception as e:
            logger.error(f"[SessionService] 批量获取会话失败: session_ids={session_ids}, error={e}")
            raise

    async def rename_session(self, context: AuthContext, session_id: str, new_title: str) -> bool:
        """
        重命名会话

        Args:
            context: 认证上下文
            session_id: 会话ID
            new_title: 新标题

        Returns:
            bool: 是否成功
        """
        try:
            logger.info(f"[SessionService] 重命名会话: user={context.user_key}, session_id={session_id}, new_title={new_title}")

            if not new_title or not new_title.strip():
                raise ValueError("新标题不能为空")

            new_title = new_title.strip()
            if len(new_title) > 200:  # 限制标题长度
                raise ValueError("标题长度不能超过200个字符")

            # 使用带权限校验的方法获取会话（需要write权限）
            session_model = await self.get_session_with_permission_check_async(context, session_id, PermissionType.WRITE)

            # 执行重命名
            success = await self.session_db_service.update_session_title_async(session_id, new_title)

            if success:
                logger.info(f"[SessionService] 会话重命名成功: user={context.user_key}, session_id={session_id}, old_title={session_model.title}, new_title={new_title}")
            else:
                logger.error(f"[SessionService] 会话重命名失败: user={context.user_key}, session_id={session_id}")

            return success

        except (SessionPermissionError, ValueError):
            raise
        except Exception as e:
            logger.error(f"[SessionService] 重命名会话异常: user={context.user_key}, session_id={session_id}, error={e}")
            raise

    async def delete_session(self, context: AuthContext, session_id: str) -> bool:
        """
        删除会话

        Args:
            context: 认证上下文
            session_id: 会话ID

        Returns:
            bool: 是否成功
        """
        try:
            logger.info(f"[SessionService] 删除会话: user={context.user_key}, session_id={session_id}")

            # 使用带权限校验的方法获取会话（需要delete权限）
            session_model = await self.get_session_with_permission_check_async(context, session_id, PermissionType.DELETE)

            # 从鉴权系统中注销资源
            try:
                from .auth_service import auth_service, ResourceType
                await asyncio.to_thread(
                    auth_service.unregister_resource,
                    context=context,
                    resource_type=ResourceType.SESSION,
                    resource_id=session_id
                )
                logger.info(f"[SessionService] 会话鉴权注销成功: session_id={session_id}")
            except Exception as auth_error:
                logger.error(f"[SessionService] 会话鉴权注销失败: session_id={session_id}, error={auth_error}")
                # 鉴权注销失败不阻止会话删除流程

            # 执行删除（软删除）
            success = await self.session_db_service.delete_session_async(session_id)

            if success:
                logger.info(f"[SessionService] 会话删除成功: user={context.user_key}, session_id={session_id}, title={session_model.title}")
            else:
                logger.error(f"[SessionService] 会话删除失败: user={context.user_key}, session_id={session_id}")

            return success

        except (SessionPermissionError, ValueError):
            raise
        except Exception as e:
            logger.error(f"[SessionService] 删除会话异常: user={context.user_key}, session_id={session_id}, error={e}")
            raise

    async def send_message(
        self,
        session_id: Optional[str],
        prompt: str,
        agent_id: str,
        desktop_id: Optional[str],
        auth_code: Optional[str],
        resources: Optional[List[SessionResource]],
        context: AuthContext
    ) -> Tuple[str, str]:
        """
        发送消息到Agent

        Args:
            session_id: 会话ID，为空则创建新会话
            prompt: 用户请求内容
            agent_id: Agent ID
            desktop_id: 桌面ID
            auth_code: token
            resources: 对话资源列表
            context: 认证上下文

        Returns:
            Tuple[str, str]: (session_id, round_id)
        """
        try:
            logger.info(f"[SessionService] 发送消息: user={context.user_key}, agent_id={agent_id}, session_id={session_id}")

            # 异步从数据库模型重建Session领域对象
            session = await self.get_or_create_session_domain_async(session_id, context.ali_uid, agent_id, context.wy_id)
            
            logger.info(f"[SessionService] 使用会话: session_id={session.session_id}")
            
            # 异步标题生成
            if not session.title or session.title.strip() == "":
                # 先用prompt的前20个字作为临时标题
                temp_title = self._generate_temp_title_from_prompt(prompt)
                await self._update_session_title_async(session.session_id, temp_title)
                # 然后异步生成正式标题
                asyncio.create_task(self._generate_session_title_async(session.session_id, prompt))

            # 处理资源
            waiy_resources = []
            if resources:
                waiy_resources = await self._process_resources(resources, context)

            # 创建WaiyInfra客户端
            waiy_client = create_waiy_infra_client()

            # 构建运行时资源配置
            runtime_resource = await self._build_runtime_resource(desktop_id, auth_code, context)

            # 获取用户设置并创建模型设置
            model_setting = None
            try:
                from .user_service import user_service
                setting_data = user_service.get_user_setting(context=context)
                model_level = setting_data.current_setting.model

                if model_level:
                    # 创建模型设置对象
                    from alibabacloud_wuyingaiinner20250708 import models as waiy_models
                    model_setting = waiy_models.MessageAsyncRequestContextModelSetting(
                        model_level=model_level
                    )
                    logger.info(f"[SessionService] 使用用户设置的模型: {model_level}")
                else:
                    logger.info(f"[SessionService] 用户未设置模型，使用默认配置")
            except Exception as e:
                logger.warning(f"[SessionService] 获取用户模型设置失败，使用默认配置: {e}")

            # 创建消息上下文
            message_context = waiy_client.create_async_message_context(
                runtime_resource=runtime_resource,
                session_id=session.session_id,
                user_id=f'ali_uid:{str(context.ali_uid)}|wy_id:{context.wy_id}',
                model_setting=model_setting
            )

            logger.info(f"[SessionService] 发送消息到waiy-infra: session_id={session.session_id}, prompt={prompt[:50]}..., desktop_id={desktop_id}, auth_code={'***' if auth_code else 'None'}")

            # 异步发送消息到WaiyInfra
            response = await asyncio.to_thread(
                waiy_client.message_async_sync,
                session_id=session_id,
                app_id=agent_id,
                message=prompt,
                context=message_context,
                resources=waiy_resources
            )

            # 从响应中提取round_id（参考session_manager的逻辑）
            if not response.body:
                raise ValueError("waiy-infra未返回有效响应")

            # 检查响应格式并提取round_id
            round_id = None
            if hasattr(response.body, 'trace_id'):
                round_id = response.body.trace_id

            if not round_id:
                raise ValueError("无法获取有效的round_id")

            session.start_processing()

            logger.info(f"[SessionService] 消息发送成功: session_id={session.session_id}, round_id={round_id}, response = {response.body}")

            return session.session_id, round_id

        except WaiyInfraClientError as e:
            logger.exception(f"[SessionService] WaiyInfra客户端错误: {e}")
            raise Exception(f"发送消息失败: {e}")
        except Exception as e:
            logger.error(f"[SessionService] 发送消息异常: user={context.user_key}, error={e}")
            raise

    async def get_or_create_session_domain_async(self, session_id: Optional[str], ali_uid: int, agent_id: str, wy_id: str = "") -> "Session":
        """
        异步获取或创建Session领域对象

        Args:
            session_id: 会话ID，为空则创建新会话
            ali_uid: 阿里云用户ID
            agent_id: Agent ID
            wy_id: 无影ID

        Returns:
            Session: Session领域对象
        """
        try:
            if session_id:
                # 异步从数据库查询现有会话
                session_model = await session_db_service.get_session_by_id_async(session_id)
                if session_model:
                    logger.info(f"[SessionService] 异步从数据库加载会话: {session_id}")
                    # 从数据库模型重建Session领域对象
                    session = self._rebuild_session_from_model(session_model)
                    return session
                else:
                    # Session不存在
                    raise ValueError(f"Session不存在: {session_id}")

            # 异步创建新会话
            new_session_id = f"sess_{uuid.uuid4().hex}"
            session_model = await asyncio.to_thread(
                session_db_service.create_session,
                ali_uid=ali_uid,
                agent_id=agent_id,
                session_id=new_session_id,
                metadata={},
                wy_id=wy_id
            )

            logger.info(f"[SessionService] 异步创建新会话: {new_session_id}")

            # 异步注册会话资源到鉴权系统
            try:
                from .auth_service import AuthContext
                auth_context = AuthContext(
                    ali_uid=ali_uid,
                    wy_id=wy_id
                )
                await asyncio.to_thread(
                    auth_service.register_resource,
                    context=auth_context,
                    resource_type=ResourceType.SESSION,
                    resource_id=new_session_id,
                    resource_name=f"会话 {new_session_id[:8]}",
                    is_public=False
                )
                logger.info(f"[SessionService] 异步会话资源注册成功: {new_session_id}")
            except Exception as e:
                logger.error(f"[SessionService] 异步会话资源注册失败: {new_session_id}, error={e}")
                # 不影响会话创建，继续执行

            # 从数据库模型重建Session领域对象
            session = self._rebuild_session_from_model(session_model)

            return session

        except Exception as e:
            logger.error(f"[SessionService] 异步获取或创建会话失败: {e}")
            raise

    async def get_or_create_session_domain(self, session_id: Optional[str], ali_uid: int, agent_id: str, wy_id: str = "") -> "Session":
        """
        获取或创建Session领域对象（参考session_manager的逻辑）

        Args:
            session_id: 会话ID，为空则创建新会话
            ali_uid: 阿里云用户ID
            agent_id: Agent ID
            wy_id: 无影ID

        Returns:
            Session: Session领域对象
        """
        try:
            if session_id:
                # 从数据库查询现有会话
                session_model = await session_db_service.get_session_by_id_async(session_id)
                if session_model:
                    logger.info(f"[SessionService] 从数据库加载会话: {session_id}")
                    # 从数据库模型重建Session领域对象
                    session = self._rebuild_session_from_model(session_model)
                    return session
                else:
                    # Session不存在
                    raise ValueError(f"Session不存在: {session_id}")

            # 创建新会话
            new_session_id = f"sess_{uuid.uuid4().hex}"
            session_model = await asyncio.to_thread(
                session_db_service.create_session,
                ali_uid=ali_uid,
                agent_id=agent_id,
                session_id=new_session_id,
                metadata={},
                wy_id=wy_id
            )


            logger.info(f"[SessionService] 创建新会话: {new_session_id}")
            
            # 注册会话资源到鉴权系统
            try:
                from .auth_service import AuthContext
                auth_context = AuthContext(
                    ali_uid=ali_uid,
                    wy_id=wy_id
                )
                await asyncio.to_thread(
                    auth_service.register_resource,
                    context=auth_context,
                    resource_type=ResourceType.SESSION,
                    resource_id=new_session_id,
                    resource_name=f"会话 {new_session_id[:8]}",
                    is_public=False
                )
                logger.info(f"[SessionService] 会话资源注册成功: {new_session_id}")
            except Exception as e:
                logger.error(f"[SessionService] 会话资源注册失败: {new_session_id}, error={e}")
                # 不影响会话创建，继续执行
            
            # 从数据库模型重建Session领域对象
            session = self._rebuild_session_from_model(session_model)

            return session

        except Exception as e:
            logger.error(f"[SessionService] 获取或创建会话失败: {e}")
            raise

    def _rebuild_session_from_model(self, session_model: SessionModel) -> SessionModel:
        """
        从数据库模型重建Session领域对象

        Args:
            session_model: 数据库Session模型

        Returns:
            Session: Session领域对象
        """
        from ...domain.models.session import Session
        from ...domain.models.enums import SessionStatus
        
        # 转换状态字符串为枚举
        status_map = {
            'CREATE': SessionStatus.CREATE,
            'ACTIVE': SessionStatus.ACTIVE,
            'CLOSED': SessionStatus.CLOSED
        }
        status = status_map.get(session_model.status, SessionStatus.CREATE)
        
        # 创建Session领域对象
        session = Session(
            session_id=session_model.session_id,
            ali_uid=session_model.ali_uid,
            agent_id=session_model.agent_id,
            wy_id=getattr(session_model, 'wy_id', ''),
            title=session_model.title,
            status=status,
            gmt_create=session_model.gmt_create,
            gmt_modified=session_model.gmt_modified,
            metadata=session_model.meta_data or {}
        )
        
        # 设置事件监听，确保状态变更能同步到数据库
        self._setup_session_events(session)
        
        return session

    def _setup_session_events(self, session: "Session"):
        """
        设置Session事件监听

        Args:
            session: Session领域对象
        """
        session.on('status_changed', self._on_session_status_changed)

    def _on_session_status_changed(self, session: "Session", old_status, new_status, reason: str):
        """
        Session状态变更回调

        Args:
            session: Session领域对象
            old_status: 旧状态
            new_status: 新状态
            reason: 变更原因
        """
        try:
            # 将状态变更同步到数据库
            status_map = {
                'CREATE': 'CREATE',
                'ACTIVE': 'ACTIVE', 
                'CLOSED': 'CLOSED'
            }
            new_status_name = getattr(new_status, 'name', str(new_status))
            new_status_str = status_map.get(new_status_name, 'CREATE')
            session_db_service.update_session_status(session.session_id, new_status_str)

            old_status_name = getattr(old_status, 'name', str(old_status))
            logger.info(f"[SessionService] Session状态已同步到数据库: {session.session_id} {old_status_name} -> {new_status_name}")
            
        except Exception as e:
            logger.error(f"[SessionService] 同步Session状态到数据库失败: {e}")

    async def _process_resources(
        self,
        resources: List[SessionResource],
        context: AuthContext
    ) -> List:
        """
        处理会话资源，转换为WaiyInfra所需的格式

        Args:
            resources: 会话资源列表
            context: 认证上下文

        Returns:
            List: WaiyInfra资源列表

        Raises:
            FileContentTooLargeError: 当文件内容总长度超过限制时抛出
        """
        waiy_resources = []
        file_content_total_length = 0  # 统计所有文件资源的content字段长度
        max_file_content_length = 200000  # 20万字符限制

        for resource in resources:
            try:
                if resource.type == ResourceType.FILE:
                    # 处理文件资源
                    file_resource = await self._process_file_resource(resource.resource_id, context)
                    if file_resource:
                        waiy_resources.append(file_resource)

                        # 检查文件资源的content字段长度
                        if hasattr(file_resource, 'content') and file_resource.content:
                            content_length = len(str(file_resource.content))
                            file_content_total_length += content_length
                            logger.debug(f"[SessionService] 文件资源content长度: file_id={resource.resource_id}, length={content_length}")

                elif resource.type == ResourceType.KNOWLEDGE_BASE:
                    # 处理知识库资源
                    kb_resource = await self._process_knowledge_base_resource(resource.resource_id, context)
                    if kb_resource:
                        waiy_resources.append(kb_resource)
                else:
                    logger.warning(f"[SessionService] 未知资源类型: type={resource.type}")
                    continue

            except Exception as e:
                logger.error(f"[SessionService] 处理资源失败: type={resource.type}, resource_id={resource.resource_id}, error={e}")
                continue

        # 检查文件内容总长度是否超过限制
        if file_content_total_length > max_file_content_length:
            logger.warning(f"[SessionService] 文件内容总长度超过限制: total_length={file_content_total_length}, limit={max_file_content_length}")
            raise FileContentTooLargeError(
                message="文件内容过多，请精简一下",
                total_length=file_content_total_length,
                limit=max_file_content_length
            )

        logger.info(f"[SessionService] 资源处理完成: 总资源数={len(waiy_resources)}, 文件内容总长度={file_content_total_length}")
        return waiy_resources

    async def _process_file_resource(
        self,
        file_id: str,
        context: AuthContext
    ):
        """
        处理文件资源

        Args:
            file_id: 文件ID
            context: 认证上下文

        Returns:
            MessageAsyncRequestResources: WaiyInfra文件资源对象
        """
        try:
            # 从文件服务获取文件信息
            file_info = file_service.get_file_info(int(file_id))

            if not file_info:
                logger.warning(f"[SessionService] 文件不存在: file_id={file_id}")
                return None

            # 检查文件权限（确保用户有权访问）
            if str(file_info.get('ali_uid')) != str(context.ali_uid):
                logger.warning(f"[SessionService] 无权访问文件: user={context.user_key}, file_id={file_id}")
                return None

            # 创建WaiyInfra客户端并构建资源对象
            waiy_client = create_waiy_infra_client()

            # 从文件名中提取文件类型（后缀名）
            file_name = file_info.get('title', '')
            file_type = ''
            if file_name and '.' in file_name:
                file_type = file_name.split('.')[-1].lower()
            
            file_resource = waiy_client.create_message_resource(
                type="file",
                address=file_info.get('download_url'),
                content=file_info.get('content'),
                file_name=file_name,
                file_size=file_info.get('file_size'),
                file_type=file_type,
                upload_time=file_info.get('gmt_created')
            )

            logger.info(f"[SessionService] 文件资源处理成功: file_id={file_id}, file_name={file_info.get('file_name')},file_resource={file_resource}")
            return file_resource

        except Exception as e:
            logger.error(f"[SessionService] 处理文件资源失败: file_id={file_id}, error={e}")
            return None

    async def _process_knowledge_base_resource(
        self,
        kb_id: str,
        context: AuthContext
    ):
        """
        处理知识库资源

        Args:
            kb_id: 知识库ID
            context: 认证上下文

        Returns:
            MessageAsyncRequestResources: WaiyInfra知识库资源对象
        """
        try:
            kb_info = knowledgebase_service.get_knowledge_base(
                kb_id=kb_id,
                auth_context=context
            )

            if not kb_info:
                logger.warning(f"[SessionService] 知识库不存在: kb_id={kb_id}")
                return None

            # 创建WaiyInfra客户端并构建知识库资源对象
            #TODO 单例
            waiy_client = create_waiy_infra_client()

            kb_resource = waiy_client.create_message_resource(
                type="knowledge_base",
                kb_id=kb_id,
                query_parameters="{\"doc_memory\":\"\"}"
            )

            logger.info(f"[SessionService] 知识库资源处理成功: kb_id={kb_id}, kb_name={kb_info.name}, kb_resource={kb_resource}")
            return kb_resource

        except Exception as e:
            logger.error(f"[SessionService] 处理知识库资源失败: kb_id={kb_id}, error={e}")
            return None

    async def _validate_desktop_and_get_region(
        self,
        desktop_id: str,
        auth_context: Optional[AuthContext]
    ) -> Optional[str]:
        """
        验证桌面ID并获取对应的 region_id

        Args:
            desktop_id: 桌面ID
            auth_context: 认证上下文

        Returns:
            Optional[str]: region_id，如果验证失败或无法获取则返回 None
        """
        if not auth_context:
            logger.warning(f"[SessionService] 缺少认证上下文，无法验证桌面环境: desktop_id={desktop_id}")
            return None

        try:
            from .user_service import user_service

            # 获取用户环境列表
            user_environments = await asyncio.to_thread(
                user_service.get_user_environments,
                auth_context
            )

            # 查找当前 desktop_id 对应的环境
            target_environment = None
            for env in user_environments.environments:
                if env.desktop_id == desktop_id:
                    target_environment = env
                    break

            if target_environment:
                # 找到对应的环境，获取 region_id
                region_id = target_environment.region_id
                logger.info(f"[SessionService] 找到桌面环境: desktop_id={desktop_id}, region_id={region_id}")
                return region_id
            else:
                # 没有找到对应的环境，记录错误并返回 None
                available_desktops = [env.desktop_id for env in user_environments.environments]
                error_msg = f"桌面 {desktop_id} 不在用户可用环境列表中。可用桌面: {available_desktops}"
                logger.error(f"[SessionService] {error_msg}")
                return None

        except Exception as e:
            # 任何异常都记录警告并返回 None
            logger.warning(f"[SessionService] 获取用户环境失败，使用默认配置: {e}")
            return None

    async def _build_runtime_resource(
        self,
        desktop_id: Optional[str],
        auth_code: Optional[str],
        auth_context: Optional[AuthContext] = None
    ):
        """
        构建运行时资源配置

        Args:
            desktop_id: 桌面ID
            auth_code: 认证代码
            auth_context: 认证上下文，用于获取STS Token

        Returns:
            MessageAsyncRequestContextRuntimeResource: 运行时资源配置对象，如果构建失败返回 None
        """
        try:
            from alibabacloud_wuyingaiinner20250708 import models as waiy_models

            token = auth_code or ""
            # 根据 desktop_id 确定资源类型
            if desktop_id and desktop_id.lower() == "agentbay":
                resource_type = "agentbay"
                # 从配置中获取 AgentBay 云资源ID
                config = env_manager.get_config()
                cloud_resource_id = ""
                token=config._get_config_value('agentbay_cloud_resource_id', 'akm-2f6c64e0-ba79-47cb-9847-d265773c7873')
                region = None
            elif desktop_id is None or desktop_id == "":
                resource_type = "none"
                cloud_resource_id = None
                region = None
            else:
                resource_type = "desktop"
                cloud_resource_id = desktop_id
                # 验证桌面并获取 region_id
                region = await self._validate_desktop_and_get_region(desktop_id, auth_context)

            # 当资源类型为 desktop 且有认证上下文时，获取 STS Token
            if resource_type == "desktop" and auth_context and desktop_id:
                sts_token = self._get_sts_token_with_cache(
                    auth_context=auth_context,
                    desktop_id=desktop_id
                )
                if sts_token:
                    token = sts_token
                    logger.info(f"[SessionService] 使用STS Token作为运行时资源token: desktop_id={desktop_id}")

            # 创建运行时资源配置对象
            runtime_resource = waiy_models.MessageAsyncRequestContextRuntimeResource(
                type=resource_type,
                token=token,
                cloud_resource_id=cloud_resource_id,
                region=region
            )

            return runtime_resource

        except Exception as e:
            logger.error(f"[SessionService] 构建运行时资源配置失败: desktop_id={desktop_id}, error={e}")
            return None

    def _get_sts_token_with_cache(
        self,
        auth_context: AuthContext,
        desktop_id: str
    ) -> Optional[str]:
        """
        获取STS Token（带缓存）

        Args:
            auth_context: 认证上下文
            desktop_id: 桌面ID

        Returns:
            str: STS Token，如果获取失败返回None
        """
        try:
            # 构建缓存键
            cache_key = f"sts_token:{auth_context.ali_uid}:{desktop_id}"

            # 初始化Redis客户端
            redis_client = RedisClient()

            # 先检查缓存
            cached_token = redis_client.get(cache_key)
            if cached_token:
                logger.info(f"[SessionService] 使用缓存的STS Token: end_user_id={auth_context.end_user_id}, desktop_id={desktop_id}")
                return cached_token

            # 缓存未命中，调用API获取新的Token
            logger.info(f"[SessionService] 缓存未命中，获取新的STS Token: end_user_id={auth_context.end_user_id}, desktop_id={desktop_id}")

            # 构建策略JSON
            policy = json.dumps({
                "Version": "1",
                "Resource": {
                    "Type": "Desktop",
                    "Id": desktop_id
                }
            }, separators=(',', ':'))  # 紧凑格式，无空格

            # 获取AppStream内部客户端
            appstream_client = get_appstream_inner_client()

            # 调用获取STS Token
            response = appstream_client.get_sts_token(
                end_user_id=auth_context.end_user_id,
                account_type=auth_context.account_type,
                policy=policy,
                user_ali_uid=auth_context.ali_uid
            )

            # 提取STS Token
            if response.body and response.body.token and response.body.token.sts_token:
                sts_token = response.body.token.sts_token

                # 缓存Token，有效期1小时（3600秒）
                redis_client.set(cache_key, sts_token, ex=3600)

                logger.info(f"[SessionService] STS Token获取成功并已缓存: end_user_id={auth_context.end_user_id}, response = {response} desktop_id={desktop_id}, session_id={response.body.token.session_id}")
                return sts_token
            else:
                logger.error(f"[SessionService] STS Token响应为空: end_user_id={auth_context.end_user_id}, desktop_id={desktop_id}")
                return None

        except AppStreamInnerClientError as e:
            logger.error(f"[SessionService] AppStream客户端错误: end_user_id={auth_context.end_user_id}, desktop_id={desktop_id}, error={e}")
            return None
        except Exception as e:
            logger.error(f"[SessionService] 获取STS Token失败: end_user_id={auth_context.end_user_id}, desktop_id={desktop_id}, error={e}")
            return None

    async def get_raw_events(
        self,
        session_id: str,
        page_size: int = 20,
        order_by: str = "desc",
        next_token: Optional[str] = None,
        include_event_types: Optional[List[str]] = None,
        exclude_event_types: Optional[List[str]] = None,
    ) -> Optional[Dict[str, Any]]:
        """
        获取原始事件数据（不转换格式）

        Args:
            session_id: 会话ID
            page_size: 每页数量，默认20
            next_token: 下一页的令牌，用于分页
            order_by: 排序顺序
            include_event_types: 包含的事件类型列表
            exclude_event_types: 排除的事件类型列表

        Returns:
            Dict: 原始事件数据，格式为 {"events": [...], "has_more": bool, "next_token": str}
        """
        try:
            logger.info(f"[SessionService] 获取原始事件: session_id={session_id}, page_size={page_size}, next_token={next_token}, order_by={order_by}")

            # 参数验证
            if not session_id:
                logger.error("[SessionService] session_id不能为空")
                return {
                    "events": [],
                    "next_token": None,
                    "has_more": False
                }

            # 从Memory SDK获取历史事件
            events_result = await asyncio.to_thread(
                self.memory_sdk.list_events,
                session_id=session_id,
                page_size=page_size,
                order_by=order_by,
                next_token=next_token,
                include_event_types=include_event_types,
                exclude_event_types=exclude_event_types
            )

            # 如果获取失败，返回空结果
            if events_result is None:
                logger.warning(f"[SessionService] Memory SDK返回None，返回空结果")
                return {
                    "events": [],
                    "next_token": None,
                    "has_more": False
                }

            return events_result

        except Exception as e:
            logger.error(f"[SessionService] 获取原始事件失败: session_id={session_id}, error={e}")
            # 返回空结果而不是None，避免上层调用出错
            return {
                "events": [],
                "next_token": None,
                "has_more": False
            }

    async def terminate_session(self, session_id: str, context: AuthContext) -> bool:
        """
        终止会话的异步任务并关闭SSE连接

        Args:
            session_id: 会话ID
            context: 用户认证上下文

        Returns:
            bool: 操作是否成功

        Raises:
            Exception: 当权限验证失败时抛出
        """
        try:
            logger.info(f"[SessionService] 开始终止会话异步任务: session_id={session_id}, user={context.user_key}")
            # 1. 权限验证
            try:
                session_model = await self.get_session_with_permission_check_async(
                    context=context,
                    session_id=session_id,
                    required_permission=PermissionType.READ
                )
                logger.info(f"[SessionService] 会话权限验证通过: session_id={session_id}")
            except Exception as perm_error:
                logger.error(f"[SessionService] 会话权限验证失败: session_id={session_id}, error={perm_error}")
                raise

            # 2. 获取会话事件，提取run_id
            events_result = await self.get_raw_events(session_id)

            if not events_result or not events_result.get("events"):
                logger.warning(f"[SessionService] 会话没有事件记录，无需终止异步任务: session_id={session_id}")
                return True

            events = events_result["events"]
            if not events:
                logger.warning(f"[SessionService] 事件列表为空，无需终止异步任务: session_id={session_id}")
                return True

            # 获取最后一条事件（最新事件）
            last_event = events[0]  # get_raw_events 默认按时间倒序排列

            # CustomEvent对象使用属性访问，不是字典访问
            run_id = getattr(last_event, 'run_id', None)

            if not run_id:
                event_id = getattr(last_event, 'event_id', 'unknown')
                logger.warning(f"[SessionService] 最新事件中没有run_id，无需终止异步任务: session_id={session_id}, event_id={event_id}")
                return True

            logger.info(f"[SessionService] 找到需要终止的run_id: session_id={session_id}, run_id={run_id}")

            # 3. 终止异步任务
            try:
                from alibabacloud_wuyingaiinner20250708 import models as waiy_models

                # 创建终止请求
                terminate_request = waiy_models.TerminateAsyncRequest()
                terminate_request.run_id = run_id
                terminate_request.app_id = session_model.agent_id
                # 调用waiy_infra_client终止异步任务
                waiy_client = create_waiy_infra_client()
                response = waiy_client.terminate_async(terminate_request)

                logger.info(f"[SessionService] 异步任务终止成功: session_id={session_id}, run_id={run_id}, response={response}")

            except WaiyInfraClientError as client_error:
                logger.error(f"[SessionService] 调用waiy_infra_client终止异步任务失败: session_id={session_id}, run_id={run_id}, error={client_error}")
                # 即使终止异步任务失败，也要继续关闭SSE连接
            except Exception as terminate_error:
                logger.error(f"[SessionService] 终止异步任务异常: session_id={session_id}, run_id={run_id}, error={terminate_error}")
                # 即使终止异步任务失败，也要继续关闭SSE连接

            # 4. 关闭SSE连接（无论异步任务终止是否成功）
            try:
                self.sse_stream_manager.close_session_connection(session_id)
                logger.info(f"[SessionService] SSE连接关闭成功: session_id={session_id}")
            except Exception as close_error:
                logger.warning(f"[SessionService] 关闭SSE连接失败: session_id={session_id}, error={close_error}")
                # SSE连接关闭失败不影响整体操作结果

            return True

        except Exception as e:
            logger.error(f"[SessionService] 终止会话失败: session_id={session_id}, error={e}")
            raise

    async def stream_history_messages(
        self,
        session_id: str,
        last_event_id: Optional[str] = None,
        page_size: int = 50
    ) -> AsyncGenerator[Tuple[Dict[str, Any], bool, str], None]:
        """
        分页流式获取历史消息，用于SSE连接

        Args:
            session_id: 会话ID
            last_event_id: 最后接收到的消息ID，用于断点续传
            page_size: 每页大小，默认50条

        Yields:
            Tuple[Dict[str, Any], bool, str]: (事件数据, 是否为会话完成, 事件ID)
        """
        try:
            logger.info(f"[SessionService] 开始流式获取历史消息: session_id={session_id}, last_event_id={last_event_id}")

            # 处理断点续传逻辑
            next_token = None
            if last_event_id:
                try:
                    # 异步调用 memory_sdk
                    last_event_id = await asyncio.to_thread(
                        self.memory_sdk.get_event_by_id,
                        last_event_id
                    )
                    if last_event_id:
                        next_token = last_event_id
                        logger.info(f"[SessionService] 断点续传，从token开始: {next_token}")
                    else:
                        logger.info(f"[SessionService] 断点续传，新会话从头开始: {session_id}")
                except Exception as e:
                    logger.error(f"[SessionService] 处理断点续传失败: {e}")

            # 分页循环获取所有历史消息
            total_events = 0
            session_finished = False
            last_event_in_batch = None
            page_count = 0

            while True:
                # 异步获取一页历史消息
                page_count += 1
                start_time = time.time()
                events_result = await self.get_raw_events(
                    session_id=session_id,
                    page_size=page_size,
                    order_by="asc",  # 按时间升序，确保消息顺序正确
                    next_token=next_token
                )
                end_time = time.time()
                query_duration = end_time - start_time
                logger.info(f"[SessionService] 分页查询耗时: session_id={session_id}, 第{page_count}页, 耗时={query_duration:.3f}秒")

                if not events_result or not events_result.get("events"):
                    logger.info(f"[SessionService] 没有更多历史消息: session_id={session_id}")
                    break

                events = events_result["events"]
                logger.info(f"[SessionService] 获取到{len(events)}条历史消息")

                # 处理当前页的事件
                for i, event in enumerate(events):
                    total_events += 1
                    last_event_in_batch = event

                    # 判断是否为当前批次的最后一条事件
                    is_last_in_batch = (i == len(events) - 1)

                    # 序列化事件数据
                    event_data = self._serialize_event_for_sse(event)
                    event_id = event_data.get('event_id', '')

                    # 检查是否为会话完成事件（只检查最后一条）
                    if is_last_in_batch and not events_result.get("has_more", False):
                        # 这是所有历史消息中的最后一条
                        session_finished = self._is_session_finished_event(event)
                        if session_finished:
                            logger.info(f"[SessionService] 检测到会话完成事件: session_id={session_id}, event_type={getattr(event, 'type', None)}")

                    yield event_data, session_finished and is_last_in_batch and not events_result.get("has_more", False), event_id

                # 检查是否还有更多数据
                if not events_result.get("has_more", False):
                    break

                next_token = events_result.get("next_token")
                if not next_token:
                    break

            logger.info(f"[SessionService] 历史消息流式发送完成: session_id={session_id}, 总计{total_events}条消息, 会话完成={session_finished}")

        except Exception as e:
            logger.error(f"[SessionService] 流式获取历史消息失败: session_id={session_id}, error={e}")
            raise

    def _serialize_event_for_sse(self, event) -> Dict[str, Any]:
        """序列化Event对象为SSE数据"""
        try:
            data = event.model_dump()

            # 处理枚举字段
            if hasattr(event, 'type') and event.type:
                data['type'] = event.type.value if hasattr(event.type, 'value') else str(event.type)

            if hasattr(event, 'role') and event.role:
                data['role'] = event.role.value if hasattr(event.role, 'value') else str(event.role)

            return data
        except Exception as e:
            logger.warning(f"[SessionService] Event序列化失败: {e}")
            return {
                'event_id': getattr(event, 'event_id', ''),
                'type': event.type.value if hasattr(event, 'type') and hasattr(event.type, 'value') else str(getattr(event, 'type', '')),
                'timestamp': getattr(event, 'timestamp', None),
                'session_id': getattr(event, 'session_id', ''),
                'run_id': getattr(event, 'run_id', ''),
                'content': getattr(event, 'content', ''),
                'role': getattr(event, 'role', None)
            }

    def _is_session_finished_event(self, event) -> bool:
        """判断事件是否为会话完成事件"""
        try:
            from memory.events import EventType

            event_type = getattr(event, 'type', None)
            if not event_type:
                return False

            # 安全获取事件类型值
            run_finished_value = EventType.RUN_FINISHED.value if hasattr(EventType.RUN_FINISHED, 'value') else str(EventType.RUN_FINISHED)
            run_error_value = EventType.RUN_ERROR.value if hasattr(EventType.RUN_ERROR, 'value') else str(EventType.RUN_ERROR)
            current_type_value = event_type.value if hasattr(event_type, 'value') else str(event_type)

            return current_type_value in [run_finished_value, run_error_value]

        except Exception as e:
            logger.error(f"[SessionService] 判断会话完成事件失败: {e}")
            return False

    def get_history_messages(self,
                             session_id: str,
                             page_size: int = 20,
                             order_by: str = "desc",
                             kb_id: Optional[str] = None,
                             next_token: Optional[str] = None) -> Dict[str,Any]:
        """
        获取会话历史消息，支持知识库关联判断

        Args:
            session_id: 会话ID
            page_size: 每页数量
            order_by: 排序方式
            kb_id: 知识库ID，如果提供则判断消息是否属于该知识库
            next_token: 分页令牌

        Returns:
            Dict: 包含消息列表和分页信息的字典
        """
        try:
            logger.info(f"[SessionService] 获取历史消息: session_id={session_id}, page_size={page_size}, kb_id={kb_id}")

            # 调用 memory_sdk 获取原始消息数据
            raw_result = self.memory_sdk.list_messages(
                session_id=session_id,
                page_size=page_size,
                order_by=order_by,
                next_token=next_token
            )

            # 提取消息列表
            messages = raw_result.get('messages', []) or raw_result.get('data', [])
            if not messages:
                logger.info(f"[SessionService] 未找到历史消息: session_id={session_id}")
                return raw_result

            # 提取所有 messageId
            message_ids = []
            for message in messages:
                message_id = None
                if hasattr(message, 'message_id'):
                    message_id = message.message_id
                elif isinstance(message, dict):
                    message_id = message.get('message_id') or message.get('messageId')

                if message_id:
                    message_ids.append(message_id)

            logger.info(f"[SessionService] 提取到 {len(message_ids)} 个消息ID")

            # 如果提供了 kb_id，则判断消息是否属于知识库
            kb_message_results = {}
            if kb_id and message_ids:
                try:
                    kb_message_results = knowledgebase_service.is_knowledge_base_message(
                        kb_id=kb_id,
                        session_id=session_id,
                        message_id_list=message_ids
                    )
                    logger.info(f"[SessionService] 知识库消息判断结果: {len(kb_message_results)} 个消息")
                except Exception as e:
                    logger.error(f"[SessionService] 知识库消息判断失败: {e}")
                    # 判断失败时，所有消息的 is_in_kb 都设置为 False

            # 处理每个消息，生成新的数据结构
            processed_messages = []
            for message in messages:
                # 提取基本字段
                message_data = self._extract_message_fields(message)

                # 获取消息ID用于知识库判断
                message_id = message_data.get('message_id')

                # 设置 is_in_kb 字段
                if kb_id and message_id and message_id in kb_message_results:
                    message_data['is_in_kb'] = kb_message_results[message_id]
                else:
                    message_data['is_in_kb'] = False

                processed_messages.append(message_data)

            # 构建返回结果
            result = {
                'messages': processed_messages,
                'next_token': raw_result.get('next_token'),
                'has_more': raw_result.get('has_more', False),
                'total_count': raw_result.get('total_count', len(processed_messages))
            }

            logger.info(f"[SessionService] 历史消息处理完成: 返回 {len(processed_messages)} 个消息")
            return result

        except Exception as e:
            logger.error(f"[SessionService] 获取历史消息失败: session_id={session_id}, error={e}")
            raise

    def set_sessions_kb_relationship(self, sessions: List[SessionInfo], kb_id: Optional[str] = None) -> None:
        """
        为会话列表设置知识库关系字段

        Args:
            sessions: 会话信息列表
            kb_id: 知识库ID，如果为空则所有会话的is_in_kb都设置为False
        """
        if not sessions:
            logger.debug("[SessionService] 会话列表为空，跳过知识库关系设置")
            return

        if not kb_id:
            # 如果没有提供kb_id，为所有会话设置is_in_kb为False
            for session in sessions:
                session.is_in_kb = False
            logger.debug(f"[SessionService] 未提供kb_id，为 {len(sessions)} 个会话设置is_in_kb=False")
            return

        try:
            # 提取所有会话ID
            session_id_list = [session.session_id for session in sessions]
            logger.info(f"[SessionService] 检查 {len(session_id_list)} 个会话是否属于知识库: kb_id={kb_id}")

            # 调用知识库服务判断会话是否属于知识库
            kb_session_results = knowledgebase_service.is_knowledge_base_session(
                kb_id=kb_id,
                session_id_list=session_id_list
            )

            logger.info(f"[SessionService] 知识库会话判断结果: {len(kb_session_results)} 个会话有结果")
            logger.info(f"[SessionService] 知识库会话判断结果: {kb_session_results} ")
            # 为每个会话设置is_in_kb字段
            for session in sessions:
                session.is_in_kb = kb_session_results.get(session.session_id, False)

            logger.info(f"[SessionService] 已为所有会话设置is_in_kb字段")

        except Exception as e:
            logger.error(f"[SessionService] 知识库会话判断失败: kb_id={kb_id}, error={e}")
            # 如果判断失败，为所有会话设置is_in_kb为False
            for session in sessions:
                session.is_in_kb = False

    def _extract_message_fields(self, message) -> Dict[str, Any]:
        """
        从消息对象中提取所需字段

        Args:
            message: 消息对象，可能是对象或字典

        Returns:
            Dict: 包含提取字段的字典
        """
        message_data = {}

        # 定义需要提取的字段映射
        field_mappings = {
            'message_id': ['message_id', 'messageId', 'id'],
            'role': ['role'],
            'content': ['content', 'text', 'message'],
            'type': ['type', 'message_type'],
            'session_id': ['session_id', 'sessionId'],
            'run_id': ['run_id', 'runId'],
            'tool_call_id': ['tool_call_id', 'toolCallId'],
            'delta': ['delta'],
            'name':['name']
        }

        # 从对象或字典中提取字段
        for target_field, source_fields in field_mappings.items():
            value = None

            # 尝试从不同的源字段获取值
            for source_field in source_fields:
                if hasattr(message, source_field):
                    value = getattr(message, source_field, None)
                    if value is not None:
                        break
                elif isinstance(message, dict):
                    value = message.get(source_field)
                    if value is not None:
                        break

            # 只保存非空值
            if value is not None:
                message_data[target_field] = value

        # 如果是对象且有 __dict__ 属性，也尝试获取其他属性
        if hasattr(message, '__dict__'):
            for key, value in message.__dict__.items():
                if not key.startswith('_') and key not in message_data and value is not None:
                    message_data[key] = value
        elif isinstance(message, dict):
            # 如果是字典，复制所有非空值
            for key, value in message.items():
                if key not in message_data and value is not None:
                    message_data[key] = value

        return message_data

    async def get_session_history(
        self,
        session_id: str,
        page_size: int = 20,
        next_token: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        获取会话历史记录
        
        Args:
            session_id: 会话ID
            page_size: 每页数量，默认20
            next_token: 下一页的令牌，用于分页
            
        Returns:
            Dict: 包含会话数据和事件的字典，格式与API文档保持一致
        """
        try:
            logger.info(f"[SessionService] 获取会话历史: session_id={session_id}, page_size={page_size}, next_token={next_token}")
            
            # 从数据库获取会话基本信息
            session_model = await self.session_db_service.get_session_by_id_async(session_id)
            if not session_model:
                logger.warning(f"[SessionService] 会话不存在: session_id={session_id}")
                return {
                    "Code": 404,
                    "Msg": "会话不存在",
                    "Data": None
                }
            
            # 获取原始事件数据
            events_result = await self.get_raw_events(
                session_id=session_id,
                page_size=page_size,
                next_token=next_token,
                exclude_event_types=[EventType.TEXT_MESSAGE_DELTA_CONTENT.value]
            )

            # 确保events_result不为None
            if events_result is None:
                events_result = {
                    "events": [],
                    "next_token": None,
                    "has_more": False
                }

            # 修复 next_token bug: 当没有更多数据时，应该返回 None
            has_more = events_result.get("has_more", False)
            next_token_result = events_result.get("next_token")

            # 如果没有更多数据或者 next_token 为 None，则强制设置为 None
            if not has_more or next_token_result is None:
                final_next_token = None
            else:
                final_next_token = next_token_result

            logger.debug(f"[SessionService] 分页结果: has_more={has_more}, "
                        f"original_next_token={next_token_result}, "
                        f"final_next_token={final_next_token}, "
                        f"events_count={len(events_result.get('events', []))}")

            return {
                "nextToken": final_next_token,
                "data": events_result.get("events", [])
            }
            
        except Exception as e:
            logger.error(f"[SessionService] 获取会话历史失败: session_id={session_id}, error={e}")
            return {
                "Code": 500,
                "Msg": f"获取会话历史失败: {str(e)}",
                "Data": None
            }

    async def create_sse_stream(self, session_id: str, last_message_id: Optional[str] = None) -> AsyncGenerator[Dict[str, Any], None]:
        """
        创建SSE流连接

        Args:
            session_id: 会话ID
            last_message_id: 最后接收到的消息ID，用于断线重连

        Yields:
            Dict[str, Any]: SSE事件数据
        """
        # 使用 SSEManager 创建 SSE 流
        async for event in self.sse_stream_manager.create_sse_stream(session_id, last_message_id):
            yield event


    def load_session_from_db(self, session_id: str):
        """从数据库加载Session - 为SSEManager提供接口（同步版本）"""
        try:
            session_model = self.session_db_service.get_session_by_id(session_id)
            if not session_model:
                return None
            return session_model
        except Exception as e:
            logger.error(f"[SessionService] 从数据库加载Session失败: session_id={session_id}, error={e}")
            return None

    async def load_session_from_db_async(self, session_id: str):
        """从数据库异步加载Session"""
        try:
            session_model = await self.session_db_service.get_session_by_id_async(session_id)
            if not session_model:
                return None
            return session_model
        except Exception as e:
            logger.error(f"[SessionService] 从数据库异步加载Session失败: session_id={session_id}, error={e}")
            return None

    def _generate_temp_title_from_prompt(self, prompt: str) -> str:
        """
        从prompt生成临时标题

        Args:
            prompt: 用户输入的提示词

        Returns:
            str: 临时标题（前20个字符，超过则加"..."）
        """
        if not prompt or not prompt.strip():
            return "新对话"

        # 去除首尾空白字符
        clean_prompt = prompt.strip()

        # 如果长度小于等于20个字符，直接返回
        if len(clean_prompt) <= 20:
            return clean_prompt

        # 截取前20个字符并添加省略号
        return clean_prompt[:20] + "..."

    async def _update_session_title_async(self, session_id: str, title: str):
        """
        异步更新会话标题

        Args:
            session_id: 会话ID
            title: 新标题
        """
        try:
            # 更新数据库中的标题（使用异步方法）
            await self.session_db_service.update_session_title_async(session_id, title)
            logger.info(f"[SessionService] 会话标题已更新: session_id={session_id}, title={title}")
        except Exception as e:
            logger.error(f"[SessionService] 更新会话标题失败: session_id={session_id}, title={title}, error={e}")

    async def _generate_session_title_async(self, session_id: str, prompt: str):
        """
        异步生成会话标题
        
        Args:
            session_id: 会话ID
            prompt: 用户消息内容
        """
        try:
            # 创建WaiyInfra客户端
            waiy_client = create_waiy_infra_client()
            
            # 调用同步消息处理方法生成标题
            response = waiy_client.message(
                app_id="chat_namer",
                message=prompt
            )

            # 提取生成的标题
            title = None
            if response and response.body:
                try:
                    response_data = None
                    if hasattr(response.body, 'response'):
                        response_data = response.body.response
                    if response_data:
                        title = str(response_data).strip()
                        logger.info(f"[SessionService] 成功提取标题: {title}")
                    else:
                        logger.warning(f"[SessionService] 未找到 response 字段，响应体: {response.body}")

                except Exception as parse_error:
                    logger.error(f"[SessionService] 解析响应失败: {parse_error}, 响应体: {response.body}")

            if title and title.strip():
                # 清理和验证标题
                title = title.strip()
                if len(title) > 200:
                    title = title[:200]
                
                # 更新数据库中的标题
                success = await self.session_db_service.update_session_title_async(session_id, title)
                
                if success:
                    logger.info(f"[SessionService] 会话标题生成成功: session_id={session_id}, title={title}")
                else:
                    logger.error(f"[SessionService] 更新会话标题失败: session_id={session_id}")
            else:
                logger.warning(f"[SessionService] 未能提取有效标题: session_id={session_id}, response={response}")
                
        except WaiyInfraClientError as e:
            logger.error(f"[SessionService] 标题生成WaiyInfra客户端错误: session_id={session_id}, error={e}")
        except Exception as e:
            logger.error(f"[SessionService] 异步生成会话标题失败: session_id={session_id}, error={e}")


# 创建全局实例
session_service = SessionService()
