"""
参数检查工具类
提供参数验证功能
"""

from typing import Any

"""错误码定义"""
PARAM_NOT_NULL = "PARAM_NOT_NULL"
PARAM_NOT_EMPTY = "PARAM_NOT_EMPTY"
PARAM_INVALID = "PARAM_INVALID"
OBJECT_NOT_FOUND = "OBJECT_NOT_FOUND"
OBJECT_ALREADY_EXISTS = "OBJECT_ALREADY_EXISTS"
PERMISSION_DENIED = "PERMISSION_DENIED"
OBJECT_DUPLICATE = "OBJECT_DUPLICATE"
OBJECT_STATE_ERROR = "OBJECT_STATE_ERROR"
OBJECT_QUOTA_LIMIT = "OBJECT_QUOTA_LIMIT"


class ClientException(Exception):
    """参数检查异常"""

    def __init__(self, message: str, code: str = "PARAM_ERROR"):
        self.message = message
        self.code = code
        super().__init__(self.message)


class CheckUtils:
    """参数检查工具类"""

    @staticmethod
    def parameter_not_null(obj: Any, parameter_name: str) -> None:
        """
        参数不能为空

        Args:
            obj: 待检查参数
            parameter_name: 参数名

        Raises:
            ClientException: 当参数为空时抛出异常
        """
        if obj is None:
            raise ClientException(f"参数不能为空: {parameter_name}", PARAM_NOT_NULL)

    @staticmethod
    def parameter_not_empty(obj: Any, parameter_name: str) -> None:
        """
        参数不能为空字符串或空列表等

        Args:
            obj: 待检查参数
            parameter_name: 参数名

        Raises:
            ClientException: 当参数为空时抛出异常
        """
        if obj is None or obj == "" or (hasattr(obj, "__len__") and len(obj) == 0):
            raise ClientException(f"参数不能为空: {parameter_name}", PARAM_NOT_EMPTY)

    @staticmethod
    def parameter_validate(condition: bool, parameter_name: str) -> None:
        """
        参数不能为空字符串或空列表等

        Args:
            condition: 待检查参数
            parameter_name: 参数名

        Raises:
            ClientException: 当参数为空时抛出异常
        """
        if not condition:
            raise ClientException(f"参数非法: {parameter_name}", PARAM_INVALID)
    @staticmethod
    def parameter_in_range(
        value: int, min_value: int, max_value: int, parameter_name: str
    ) -> None:
        """
        参数值在指定范围内

        Args:
            value: 待检查的值
            min_value: 最小值
            max_value: 最大值
            parameter_name: 参数名

        Raises:
            ClientException: 当参数值不在范围内时抛出异常
        """
        if value < min_value or value > max_value:
            raise ClientException(
                f"参数 {parameter_name} 的值必须在 {min_value} 到 {max_value} 之间",
                PARAM_INVALID,
            )

    @staticmethod
    def object_exists(obj: Any, parameter_name: str) -> None:
        """
        检查对象是否存在（不为None且不为空）

        Args:
            obj: 待检查对象
            parameter_name: 参数名

        Raises:
            ClientException: 当对象不存在时抛出异常
        """
        if obj is None or obj == "" or (hasattr(obj, "__len__") and len(obj) == 0):
            raise ClientException(f"对象不存在: {parameter_name}", OBJECT_NOT_FOUND)

    @staticmethod
    def object_not_exists(obj: Any, parameter_name: str) -> None:
        """
        检查对象是否不存在（为None或为空）

        Args:
            obj: 待检查对象
            parameter_name: 参数名

        Raises:
            ClientException: 当对象存在时抛出异常
        """
        if obj is not None or (hasattr(obj, "__len__") and len(obj) > 0):
            raise ClientException(
                f"对象已存在: {parameter_name}", OBJECT_ALREADY_EXISTS
            )

    @staticmethod
    def object_exists_if(condition: bool, parameter_name: str) -> None:
        """
        当条件为True时，检查对象是否存在（不为None且不为空）

        Args:
            condition: 检查条件
            parameter_name: 参数名

        Raises:
            ClientException: 当条件为True且对象不存在时抛出异常
        """
        if not condition:
            raise ClientException(f"对象不存在: {parameter_name}", OBJECT_NOT_FOUND)

    @staticmethod
    def object_not_exists_if(condition: bool, parameter_name: str) -> None:
        """
        当条件为True时，检查对象是否不存在（为None或为空）

        Args:
            condition: 检查条件
            parameter_name: 参数名

        Raises:
            ClientException: 当条件为True且对象存在时抛出异常
        """
        if not condition:
            raise ClientException(
                f"对象已存在: {parameter_name}", OBJECT_ALREADY_EXISTS
            )

    @staticmethod
    def permission_check(condition: bool, object_name: str) -> None:
        """
        检查权限

        Args:
            condition: 检查条件
            object_name: 参数名

        Raises:
            ClientException: 当条件为False时抛出权限异常
        """
        if not condition:
            raise ClientException(f"无权限: {object_name}", PERMISSION_DENIED)

    @staticmethod
    def object_duplicate_if(condition: bool, parameter_name: str) -> None:
        """
        当条件为True时，检查对象是否重复

        Args:
            condition: 检查条件
            parameter_name: 参数名

        Raises:
            ClientException: 当条件为True时抛出重复异常
        """
        if not condition:
            raise ClientException(f"同名对象已存在: {parameter_name}", OBJECT_DUPLICATE)

    @staticmethod
    def object_state(condition: bool, parameter_name: str) -> None:
        """
        检查对象状态
        """
        if not condition:
            raise ClientException(f"对象状态不可操作: {parameter_name}", OBJECT_STATE_ERROR)

    @staticmethod
    def quota_limit(condition: bool, parameter_name: str) -> None:
        """
        检查限额
        """
        if not condition:
            raise ClientException(f"对象操作限额: {parameter_name}", OBJECT_QUOTA_LIMIT)
